import { BrowserWindow, ipc<PERSON>ain, shell } from 'electron'
import { configManager } from './config-manager'
import { state } from '../index'
import { ChessManager } from './chess-manager'
import { RealtimeMonitor } from './realtime-monitor'
export interface IIPCHandler {
  getMainWindow: () => BrowserWindow | null
  takeScreenshot: () => Promise<string>
  getImagePreview: (filePath: string) => Promise<string>
  clearQueues: () => void
  setView: (view: 'analysis' | 'history') => void
  getView: () => 'analysis' | 'history'
  getScreenshotQueue: () => string[]
  getExtraScreenshotQueue: () => string[]
  moveWindowLeft: () => void
  moveWindowRight: () => void
  moveWindowUp: () => void
  moveWindowDown: () => void
  toggleMainWindow: () => void
  isVisible: () => boolean
  deleteScreenshot: (path: string) => Promise<{ success: boolean; error?: string }>
  CHESS_EVENTS: typeof state.CHESS_EVENTS
  chessManager: ChessManager | null
  realtimeMonitor: RealtimeMonitor | null
  setWindowDimensions: (width: number, height: number) => void
  getCurrentPosition: () => any
  setCurrentPosition: (position: any) => void
  getIsAnalyzing: () => boolean
  setIsAnalyzing: (analyzing: boolean) => void
}

export function initializeIpcHandler(deps: IIPCHandler): void {
  ipcMain.handle('get-config', () => {
    return configManager.loadConfig()
  })

  ipcMain.handle('update-config', (_event, updates) => {
    return configManager.updateConfig(updates)
  })

  // Chess-specific IPC handlers
  ipcMain.handle('get-current-position', () => {
    return deps.getCurrentPosition()
  })

  ipcMain.handle('get-is-analyzing', () => {
    return deps.getIsAnalyzing()
  })

  ipcMain.handle('stop-chess-analysis', () => {
    deps.chessManager?.stopAnalysis()
    deps.setIsAnalyzing(false)
    return { success: true }
  })

  ipcMain.handle('get-best-move', () => {
    return deps.chessManager?.getBestMoveHighlight() || null
  })

  ipcMain.handle('get-evaluation', () => {
    return deps.chessManager?.getEvaluationText() || 'No position'
  })

  ipcMain.handle('get-square-info', (_event, file: string, rank: number) => {
    return deps.chessManager?.getSquareInfo(file, rank) || null
  })

  ipcMain.handle('get-all-squares', () => {
    return deps.chessManager?.getAllSquareNames() || []
  })

  // Real-time monitoring IPC handlers
  ipcMain.handle('start-realtime-monitoring', async (_event, playerColor: 'white' | 'black') => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: 'Real-time monitor not available' }
      }
      const result = await deps.realtimeMonitor.startMonitoring(playerColor)
      return result
    } catch (error) {
      console.error('Error starting real-time monitoring:', error)
      return { success: false, error: 'Failed to start monitoring' }
    }
  })

  ipcMain.handle('stop-realtime-monitoring', () => {
    try {
      if (deps.realtimeMonitor) {
        deps.realtimeMonitor.stopMonitoring()
      }
      return { success: true }
    } catch (error) {
      console.error('Error stopping real-time monitoring:', error)
      return { success: false, error: 'Failed to stop monitoring' }
    }
  })

  ipcMain.handle('get-monitoring-state', () => {
    return deps.realtimeMonitor?.getState() || {
      isActive: false,
      website: null,
      playerColor: null,
      currentFen: null,
      lastMoveTime: 0,
      analysisCount: 0
    }
  })

  ipcMain.handle('start-quick-mode', async (_event, playerColor: 'white' | 'black') => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: 'Real-time monitor not available' }
      }
      await deps.realtimeMonitor.startQuickMode(playerColor)
      return { success: true }
    } catch (error) {
      console.error('Error starting quick mode:', error)
      return { success: false, error: 'Failed to start quick mode' }
    }
  })

  ipcMain.handle('simulate-game', async () => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: 'Real-time monitor not available' }
      }
      await deps.realtimeMonitor.simulateGame()
      return { success: true }
    } catch (error) {
      console.error('Error simulating game:', error)
      return { success: false, error: 'Failed to simulate game' }
    }
  })

  ipcMain.handle('analyze-manual-position', async (_event, fen: string) => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: 'Real-time monitor not available' }
      }
      await deps.realtimeMonitor.analyzeManualPosition(fen)
      return { success: true }
    } catch (error) {
      console.error('Error analyzing manual position:', error)
      return { success: false, error: 'Failed to analyze position' }
    }
  })

  ipcMain.handle('get-screenshots', async () => {
    try {
      let previews: { path: string; preview: string }[] = []
      const currentView = deps.getView()
      console.log('currentView', currentView)

      // Always use main screenshot queue for chess analysis
      const queue = deps.getScreenshotQueue()
      previews = await Promise.all(
        queue.map(async (path) => {
          const preview = await deps.getImagePreview(path)
          return { path, preview }
        })
      )

      return previews
    } catch (error) {
      console.error('Error getting screenshots:', error)
      throw error
    }
  })
  ipcMain.handle('delete-screenshot', async (_, path: string) => {
    return deps.deleteScreenshot(path)
  })
  ipcMain.handle('trigger-screenshot', async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      try {
        const screenshotPath = await deps.takeScreenshot()
        const preview = await deps.getImagePreview(screenshotPath)
        mainWindow.webContents.send('screenshot-taken', { path: screenshotPath, preview })
        return { success: true }
      } catch (error) {
        console.error('Error triggering screenshot:', error)
        return { success: false, error: 'Failed to take screenshot' }
      }
    }
    return { success: false, error: 'Main window not found' }
  })
  ipcMain.handle('toggle-main-window', async () => {
    return deps.toggleMainWindow()
  })
  ipcMain.handle('delete-last-screenshot', async () => {
    try {
      const queue =
        deps.getView() === 'queue' ? deps.getScreenshotQueue() : deps.getExtraScreenshotQueue()
      console.log('queue', queue)

      if (queue.length === 0) {
        return { success: false, error: 'No screenshots to delete' }
      }

      const lastScreenshot = queue[queue.length - 1]
      const result = await deps.deleteScreenshot(lastScreenshot)

      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('screenshot-deleted')
      }

      return result
    } catch (error) {
      console.error('Error deleting last screenshot:', error)
      return { success: false, error: 'Failed to delete screenshot' }
    }
  })
  ipcMain.handle('open-settings-portal', async () => {
    const mainWindow = deps.getMainWindow()
    if (mainWindow) {
      mainWindow.webContents.send('show-settings-dialog')
      return { success: true }
    }
    return { success: false, error: 'Main window not found' }
  })
  ipcMain.handle('trigger-chess-analysis', async () => {
    try {
      // Chess analysis is automatically triggered when taking screenshots
      // This can be used to force re-analysis
      const mainWindow = deps.getMainWindow()
      if (mainWindow) {
        mainWindow.webContents.send('chess-force-analysis')
      }
      return { success: true }
    } catch (error) {
      console.error('Error triggering chess analysis:', error)
      return { success: false, error: 'Failed to trigger analysis' }
    }
  })
  ipcMain.handle('trigger-reset', async () => {
    try {
      deps.chessManager?.stopAnalysis()
      deps.setIsAnalyzing(false)

      deps.clearQueues()
      deps.setView('analysis')

      const mainWindow = deps.getMainWindow()
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send('chess-reset-view')
      }
      return { success: true }
    } catch (error) {
      console.error('Error triggering reset:', error)
      return { success: false, error: 'Failed to reset' }
    }
  })
  ipcMain.handle('set-window-dimensions', (_, width: number, height: number) => {
    return deps.setWindowDimensions(width, height)
  })
  ipcMain.handle(
    'update-content-dimensions',
    async (_, { width, height }: { width: number; height: number }) => {
      // console.log('update-content-dimensions', width, height)
      if (width && height) {
        deps.setWindowDimensions(width, height)
      }
    }
  )
  ipcMain.handle('openLink', (_, url: string) => {
    try {
      console.log('openLink', url)
      shell.openExternal(url)
      return { success: true }
    } catch (error) {
      console.error('Error opening link:', error)
      return { success: false, error: 'Failed to open link' }
    }
  })
}
