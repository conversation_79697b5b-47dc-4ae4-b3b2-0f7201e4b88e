import { useEffect, useState } from 'react'
import { cn } from '../lib/utils'

interface MoveHighlight {
  from: { x: number; y: number; width: number; height: number }
  to: { x: number; y: number; width: number; height: number }
  move: {
    from: string
    to: string
    san: string
    uci: string
  }
}

interface MoveOverlayProps {
  highlight: MoveHighlight | null
  visible: boolean
  className?: string
}

export function MoveOverlay({ highlight, visible, className }: MoveOverlayProps) {
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    if (highlight && visible) {
      setIsAnimating(true)
      const timer = setTimeout(() => setIsAnimating(false), 1000)
      return () => clearTimeout(timer)
    }
  }, [highlight, visible])

  if (!highlight || !visible) {
    return null
  }

  return (
    <div className={cn("fixed inset-0 pointer-events-none z-50", className)}>
      {/* From square highlight */}
      <div
        className={cn(
          "absolute border-4 border-red-500 bg-red-500/20 rounded-lg transition-all duration-500",
          isAnimating && "animate-pulse"
        )}
        style={{
          left: highlight.from.x,
          top: highlight.from.y,
          width: highlight.from.width,
          height: highlight.from.height,
        }}
      >
        <div className="absolute -top-8 left-0 bg-red-500 text-white px-2 py-1 rounded text-xs font-bold">
          {highlight.move.from}
        </div>
      </div>

      {/* To square highlight */}
      <div
        className={cn(
          "absolute border-4 border-green-500 bg-green-500/20 rounded-lg transition-all duration-500",
          isAnimating && "animate-pulse"
        )}
        style={{
          left: highlight.to.x,
          top: highlight.to.y,
          width: highlight.to.width,
          height: highlight.to.height,
        }}
      >
        <div className="absolute -top-8 left-0 bg-green-500 text-white px-2 py-1 rounded text-xs font-bold">
          {highlight.move.to}
        </div>
      </div>

      {/* Arrow connecting the squares */}
      <svg
        className="absolute inset-0 w-full h-full"
        style={{ pointerEvents: 'none' }}
      >
        <defs>
          <marker
            id="arrowhead"
            markerWidth="10"
            markerHeight="7"
            refX="9"
            refY="3.5"
            orient="auto"
          >
            <polygon
              points="0 0, 10 3.5, 0 7"
              fill="#3b82f6"
            />
          </marker>
        </defs>
        <line
          x1={highlight.from.x + highlight.from.width / 2}
          y1={highlight.from.y + highlight.from.height / 2}
          x2={highlight.to.x + highlight.to.width / 2}
          y2={highlight.to.y + highlight.to.height / 2}
          stroke="#3b82f6"
          strokeWidth="4"
          markerEnd="url(#arrowhead)"
          className={cn(
            "transition-all duration-500",
            isAnimating && "animate-pulse"
          )}
        />
      </svg>

      {/* Move notation display */}
      <div className="absolute top-4 right-4 bg-black/80 text-white px-4 py-2 rounded-lg">
        <div className="text-lg font-bold">{highlight.move.san}</div>
        <div className="text-sm text-gray-300">{highlight.move.uci}</div>
      </div>
    </div>
  )
}

interface ChessBoardOverlayProps {
  boardBounds?: { x: number; y: number; width: number; height: number }
  squares?: Array<{
    file: string
    rank: number
    piece: string | null
    x: number
    y: number
    width: number
    height: number
  }>
  showSquareNames?: boolean
  className?: string
}

export function ChessBoardOverlay({ 
  boardBounds, 
  squares, 
  showSquareNames = false, 
  className 
}: ChessBoardOverlayProps) {
  if (!boardBounds || !squares) {
    return null
  }

  return (
    <div className={cn("fixed inset-0 pointer-events-none z-40", className)}>
      {/* Board outline */}
      <div
        className="absolute border-2 border-blue-400/50 rounded-lg"
        style={{
          left: boardBounds.x,
          top: boardBounds.y,
          width: boardBounds.width,
          height: boardBounds.height,
        }}
      />

      {/* Square names */}
      {showSquareNames && squares.map((square) => (
        <div
          key={`${square.file}${square.rank}`}
          className="absolute text-xs font-bold text-white bg-black/50 px-1 rounded"
          style={{
            left: square.x + 2,
            top: square.y + 2,
          }}
        >
          {square.file}{square.rank}
        </div>
      ))}

      {/* Piece indicators */}
      {squares.map((square) => (
        square.piece && (
          <div
            key={`piece-${square.file}${square.rank}`}
            className="absolute text-center text-white bg-black/30 rounded-full w-6 h-6 flex items-center justify-center text-xs"
            style={{
              left: square.x + square.width - 28,
              top: square.y + square.height - 28,
            }}
          >
            {getPieceSymbol(square.piece)}
          </div>
        )
      ))}
    </div>
  )
}

// Helper function to get piece symbols
function getPieceSymbol(piece: string): string {
  const symbols: { [key: string]: string } = {
    'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙',
    'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟'
  }
  return symbols[piece] || piece
}

interface AnalysisOverlayProps {
  evaluation?: string
  bestMove?: string
  depth?: number
  confidence?: number
  className?: string
}

export function AnalysisOverlay({ 
  evaluation, 
  bestMove, 
  depth, 
  confidence, 
  className 
}: AnalysisOverlayProps) {
  return (
    <div className={cn("fixed top-4 left-4 bg-black/80 text-white p-4 rounded-lg z-50", className)}>
      <div className="space-y-2">
        <div className="text-lg font-bold text-blue-400">Chessly Analysis</div>
        
        {evaluation && (
          <div className="flex justify-between">
            <span className="text-gray-300">Evaluation:</span>
            <span className="font-medium">{evaluation}</span>
          </div>
        )}
        
        {bestMove && (
          <div className="flex justify-between">
            <span className="text-gray-300">Best Move:</span>
            <span className="font-medium text-green-400">{bestMove}</span>
          </div>
        )}
        
        {depth !== undefined && (
          <div className="flex justify-between">
            <span className="text-gray-300">Depth:</span>
            <span className="font-medium">{depth}</span>
          </div>
        )}
        
        {confidence !== undefined && (
          <div className="flex justify-between">
            <span className="text-gray-300">Confidence:</span>
            <span className="font-medium">{(confidence * 100).toFixed(1)}%</span>
          </div>
        )}
      </div>
    </div>
  )
}

interface StatusOverlayProps {
  status: 'idle' | 'capturing' | 'analyzing' | 'ready' | 'error'
  message?: string
  className?: string
}

export function StatusOverlay({ status, message, className }: StatusOverlayProps) {
  const getStatusColor = () => {
    switch (status) {
      case 'idle': return 'text-gray-400'
      case 'capturing': return 'text-blue-400'
      case 'analyzing': return 'text-yellow-400'
      case 'ready': return 'text-green-400'
      case 'error': return 'text-red-400'
      default: return 'text-gray-400'
    }
  }

  const getStatusIcon = () => {
    switch (status) {
      case 'capturing': return '📷'
      case 'analyzing': return '🧠'
      case 'ready': return '✅'
      case 'error': return '❌'
      default: return '⏸️'
    }
  }

  return (
    <div className={cn("fixed bottom-4 right-4 bg-black/80 text-white px-4 py-2 rounded-lg z-50", className)}>
      <div className="flex items-center gap-2">
        <span className="text-lg">{getStatusIcon()}</span>
        <div>
          <div className={cn("font-medium", getStatusColor())}>
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </div>
          {message && (
            <div className="text-sm text-gray-300">{message}</div>
          )}
        </div>
      </div>
    </div>
  )
}
