import { BrowserWindow, webContents } from 'electron'
import { ChessManager } from './chess-manager'

export interface ChessWebsiteConfig {
  name: string
  domains: string[]
  boardSelector: string
  pieceSelector: string
  highlightContainer: string
  getFenScript: string
  injectHighlightScript: string
}

export class BrowserAutomation {
  private isMonitoring = false
  private currentWebContents: Electron.WebContents | null = null
  private playerColor: 'w' | 'b' = 'w'
  private lastFen = ''
  private monitorInterval: NodeJS.Timeout | null = null
  private chessManager: ChessManager
  private onPositionChange?: (fen: string) => void
  private onError?: (error: string) => void

  // Chess website configurations
  private readonly websites: ChessWebsiteConfig[] = [
    {
      name: 'Chess.com',
      domains: ['chess.com', 'www.chess.com'],
      boardSelector: '.board',
      pieceSelector: '.piece',
      highlightContainer: 'wc-chess-board',
      getFenScript: this.getChessComFenScript(),
      injectHighlightScript: this.getChessComHighlightScript()
    },
    {
      name: 'Lichess',
      domains: ['lichess.org', 'www.lichess.org'],
      boardSelector: 'cg-board',
      pieceSelector: 'piece',
      highlightContainer: 'cg-board',
      getFenScript: this.getLichessFenScript(),
      injectHighlightScript: this.getLichessHighlightScript()
    }
  ]

  constructor(chessManager: ChessManager) {
    this.chessManager = chessManager
  }

  public async startMonitoring(
    playerColor: 'white' | 'black',
    onPositionChange?: (fen: string) => void,
    onError?: (error: string) => void
  ): Promise<{ success: boolean; error?: string }> {
    try {
      if (this.isMonitoring) {
        return { success: false, error: 'Already monitoring' }
      }

      this.playerColor = playerColor === 'white' ? 'w' : 'b'
      this.onPositionChange = onPositionChange
      this.onError = onError

      // Find chess website in open browser windows
      const result = await this.findChessWebsite()
      if (!result.success) {
        return result
      }

      // Start monitoring
      this.isMonitoring = true
      this.startPositionMonitoring()

      console.log(`Started monitoring chess website as ${playerColor}`)
      return { success: true }
    } catch (error) {
      console.error('Error starting browser automation:', error)
      return { success: false, error: 'Failed to start monitoring' }
    }
  }

  public stopMonitoring(): void {
    this.isMonitoring = false
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval)
      this.monitorInterval = null
    }
    this.currentWebContents = null
    this.lastFen = ''
    console.log('Stopped browser monitoring')
  }

  private async findChessWebsite(): Promise<{ success: boolean; error?: string }> {
    try {
      // Get all web contents (browser windows/tabs)
      const allWebContents = webContents.getAllWebContents()
      
      for (const wc of allWebContents) {
        try {
          const url = wc.getURL()
          const website = this.websites.find(site => 
            site.domains.some(domain => url.includes(domain))
          )
          
          if (website) {
            // Check if chess board is present
            const hasBoardResult = await wc.executeJavaScript(`
              (function() {
                try {
                  const board = document.querySelector('${website.boardSelector}');
                  return board !== null;
                } catch (e) {
                  return false;
                }
              })()
            `)
            
            if (hasBoardResult) {
              this.currentWebContents = wc
              console.log(`Found ${website.name} with chess board`)
              return { success: true }
            }
          }
        } catch (error) {
          // Skip this web content if we can't access it
          continue
        }
      }

      return { 
        success: false, 
        error: 'No chess website found. Please open chess.com or lichess.org in your browser and start a game.' 
      }
    } catch (error) {
      return { success: false, error: 'Failed to find chess website' }
    }
  }

  private startPositionMonitoring(): void {
    this.monitorInterval = setInterval(async () => {
      if (!this.isMonitoring || !this.currentWebContents) {
        return
      }

      try {
        const fen = await this.getCurrentFen()
        if (fen && fen !== this.lastFen) {
          this.lastFen = fen
          console.log('Position changed:', fen)
          
          if (this.onPositionChange) {
            this.onPositionChange(fen)
          }

          // Analyze position
          await this.chessManager.analyzePosition(fen, this.playerColor)
        }
      } catch (error) {
        console.error('Error monitoring position:', error)
        if (this.onError) {
          this.onError('Failed to read chess position')
        }
      }
    }, 1000) // Check every second
  }

  private async getCurrentFen(): Promise<string | null> {
    if (!this.currentWebContents) {
      return null
    }

    try {
      const url = this.currentWebContents.getURL()
      const website = this.websites.find(site => 
        site.domains.some(domain => url.includes(domain))
      )

      if (!website) {
        return null
      }

      // Execute FEN extraction script
      const fen = await this.currentWebContents.executeJavaScript(website.getFenScript)
      return fen ? `${fen} ${this.playerColor}` : null
    } catch (error) {
      console.error('Error getting FEN:', error)
      return null
    }
  }

  public async highlightBestMove(from: string, to: string): Promise<void> {
    if (!this.currentWebContents || !this.isMonitoring) {
      return
    }

    try {
      const url = this.currentWebContents.getURL()
      const website = this.websites.find(site => 
        site.domains.some(domain => url.includes(domain))
      )

      if (!website) {
        return
      }

      // Inject highlight script
      await this.currentWebContents.executeJavaScript(`
        ${website.injectHighlightScript}
        highlightMove('${from}', '${to}');
      `)
    } catch (error) {
      console.error('Error highlighting move:', error)
    }
  }

  private getChessComFenScript(): string {
    return `
      (function() {
        try {
          function getFenString() {
            let fen_string = "";
            for(var i = 8; i >= 1; i--) {
              for(var j = 1; j <= 8; j++) {
                let position = \`\${j}\${i}\`;
                if(j == 1 && i != 8) {
                  fen_string += "/";
                }
                let piece_in_position = document.querySelectorAll(\`.piece.square-\${position}\`)[0]?.classList ?? null;
                if(piece_in_position != null) {
                  for(var item of piece_in_position.values()) {
                    if(item.length == 2) {
                      piece_in_position = item;
                    }
                  }
                }
                if(piece_in_position == null) {
                  let previous_char = fen_string.split("").pop();
                  if(!isNaN(Number(previous_char))) {
                    fen_string = fen_string.substring(0, fen_string.length - 1);
                    fen_string += Number(previous_char) + 1;
                  } else {
                    fen_string += "1";
                  }
                } else if(piece_in_position?.split("")[0] == "b") {
                  fen_string += piece_in_position.split("")[1];
                } else if(piece_in_position?.split("")[0] == "w") {
                  fen_string += piece_in_position.split("")[1].toUpperCase();
                }
              }
            }
            return fen_string;
          }
          return getFenString();
        } catch (e) {
          return null;
        }
      })()
    `
  }

  private getChessComHighlightScript(): string {
    return `
      function highlightMove(from, to) {
        try {
          // Remove previous highlights
          document.querySelectorAll(".chessly-highlight").forEach(el => el.remove());
          
          const char_map = {"a":1,"b":2,"c":3,"d":4,"e":5,"f":6,"g":7,"h":8};
          const fromSquare = \`\${char_map[from[0]]}\${from[1]}\`;
          const toSquare = \`\${char_map[to[0]]}\${to[1]}\`;
          
          // Create highlight elements
          const fromHighlight = document.createElement("div");
          fromHighlight.className = \`highlight chessly-highlight square-\${fromSquare}\`;
          fromHighlight.style = "background: #ff6b6b; opacity: 0.7; pointer-events: none; z-index: 10;";
          
          const toHighlight = document.createElement("div");
          toHighlight.className = \`highlight chessly-highlight square-\${toSquare}\`;
          toHighlight.style = "background: #51cf66; opacity: 0.7; pointer-events: none; z-index: 10;";
          
          const board = document.querySelector("wc-chess-board") || document.querySelector(".board");
          if (board) {
            board.appendChild(fromHighlight);
            board.appendChild(toHighlight);
          }
        } catch (e) {
          console.error("Error highlighting move:", e);
        }
      }
    `
  }

  private getLichessFenScript(): string {
    return `
      (function() {
        try {
          // Lichess FEN extraction - more complex due to different DOM structure
          const pieces = document.querySelectorAll('cg-board piece');
          const board = {};
          
          pieces.forEach(piece => {
            const pos = piece.getAttribute('cgKey');
            const role = piece.getAttribute('cgRole');
            const color = piece.getAttribute('cgColor');
            
            if (pos && role && color) {
              const file = pos[0];
              const rank = parseInt(pos[1]);
              const pieceChar = color === 'white' ? role[0].toUpperCase() : role[0];
              board[pos] = pieceChar;
            }
          });
          
          let fen = '';
          for (let rank = 8; rank >= 1; rank--) {
            let emptyCount = 0;
            for (let file of 'abcdefgh') {
              const square = file + rank;
              if (board[square]) {
                if (emptyCount > 0) {
                  fen += emptyCount;
                  emptyCount = 0;
                }
                fen += board[square];
              } else {
                emptyCount++;
              }
            }
            if (emptyCount > 0) {
              fen += emptyCount;
            }
            if (rank > 1) {
              fen += '/';
            }
          }
          
          return fen;
        } catch (e) {
          return null;
        }
      })()
    `
  }

  private getLichessHighlightScript(): string {
    return `
      function highlightMove(from, to) {
        try {
          // Remove previous highlights
          document.querySelectorAll(".chessly-highlight").forEach(el => el.remove());
          
          // Create highlight elements for Lichess
          const board = document.querySelector('cg-board');
          if (!board) return;
          
          const fromEl = document.createElement('div');
          fromEl.className = 'chessly-highlight';
          fromEl.style = \`
            position: absolute;
            background: rgba(255, 107, 107, 0.7);
            width: 12.5%;
            height: 12.5%;
            pointer-events: none;
            z-index: 10;
            left: \${(from.charCodeAt(0) - 97) * 12.5}%;
            bottom: \${(parseInt(from[1]) - 1) * 12.5}%;
          \`;
          
          const toEl = document.createElement('div');
          toEl.className = 'chessly-highlight';
          toEl.style = \`
            position: absolute;
            background: rgba(81, 207, 102, 0.7);
            width: 12.5%;
            height: 12.5%;
            pointer-events: none;
            z-index: 10;
            left: \${(to.charCodeAt(0) - 97) * 12.5}%;
            bottom: \${(parseInt(to[1]) - 1) * 12.5}%;
          \`;
          
          board.appendChild(fromEl);
          board.appendChild(toEl);
        } catch (e) {
          console.error("Error highlighting move:", e);
        }
      }
    `
  }

  public isCurrentlyMonitoring(): boolean {
    return this.isMonitoring
  }

  public getCurrentWebsite(): string | null {
    if (!this.currentWebContents) {
      return null
    }

    const url = this.currentWebContents.getURL()
    const website = this.websites.find(site => 
      site.domains.some(domain => url.includes(domain))
    )
    
    return website?.name || null
  }
}
