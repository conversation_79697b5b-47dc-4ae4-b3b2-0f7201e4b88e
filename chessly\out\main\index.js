"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const path = require("path");
const utils = require("@electron-toolkit/utils");
const fs = require("fs");
const events = require("events");
const screenshot = require("screenshot-desktop");
const uuid = require("uuid");
const child_process = require("child_process");
const util = require("util");
const icon = path.join(__dirname, "../../resources/icon.png");
class ConfigManager extends events.EventEmitter {
  configPath;
  defaultConfig = {
    opacity: 0.9,
    autoAnalyze: true,
    showSquareNames: false,
    highlightColor: "#3b82f6"
  };
  constructor() {
    super();
    try {
      this.configPath = path.join(electron.app.getPath("userData"), "config.json");
      console.log("Config path:", this.configPath);
    } catch (error) {
      console.error("Error getting config path:", error);
      this.configPath = path.join(process.cwd(), "config.json");
    }
    this.ensureConfigFileExists();
  }
  ensureConfigFileExists() {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig);
        console.log("Created default config file:", this.configPath);
      }
    } catch (error) {
      console.error("Failed to ensure config file exists:", error);
    }
  }
  saveConfig(config) {
    try {
      const configDir = path.dirname(this.configPath);
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true });
      }
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2));
    } catch (error) {
      console.error("Failed to save config:", error);
    }
  }
  loadConfig() {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, "utf-8");
        const config = JSON.parse(configData);
        return {
          ...this.defaultConfig,
          ...config
        };
      }
      this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    } catch (error) {
      console.error("Failed to load config:", error);
      return this.defaultConfig;
    }
  }
  updateConfig(updates) {
    try {
      const currentConfig = this.loadConfig();
      const newConfig = {
        ...currentConfig,
        ...updates
      };
      this.saveConfig(newConfig);
      this.emit("config-updated", newConfig);
      return newConfig;
    } catch (error) {
      console.error("Failed to update config:", error);
      return this.defaultConfig;
    }
  }
  getOpacity() {
    const config = this.loadConfig();
    return config.opacity !== void 0 ? config.opacity : 0.9;
  }
  setOpacity(opacity) {
    const validOpacity = Math.min(1, Math.max(0.1, opacity));
    this.updateConfig({ opacity: validOpacity });
  }
  getAutoAnalyze() {
    const config = this.loadConfig();
    return config.autoAnalyze !== void 0 ? config.autoAnalyze : true;
  }
  setAutoAnalyze(autoAnalyze) {
    this.updateConfig({ autoAnalyze });
  }
  getShowSquareNames() {
    const config = this.loadConfig();
    return config.showSquareNames !== void 0 ? config.showSquareNames : false;
  }
  setShowSquareNames(showSquareNames) {
    this.updateConfig({ showSquareNames });
  }
  getHighlightColor() {
    const config = this.loadConfig();
    return config.highlightColor !== void 0 ? config.highlightColor : "#3b82f6";
  }
  setHighlightColor(highlightColor) {
    this.updateConfig({ highlightColor });
  }
}
const configManager = new ConfigManager();
function initializeIpcHandler(deps) {
  electron.ipcMain.handle("get-config", () => {
    return configManager.loadConfig();
  });
  electron.ipcMain.handle("update-config", (_event, updates) => {
    return configManager.updateConfig(updates);
  });
  electron.ipcMain.handle("get-current-position", () => {
    return deps.getCurrentPosition();
  });
  electron.ipcMain.handle("get-is-analyzing", () => {
    return deps.getIsAnalyzing();
  });
  electron.ipcMain.handle("stop-chess-analysis", () => {
    deps.chessManager?.stopAnalysis();
    deps.setIsAnalyzing(false);
    return { success: true };
  });
  electron.ipcMain.handle("get-best-move", () => {
    return deps.chessManager?.getBestMoveHighlight() || null;
  });
  electron.ipcMain.handle("get-evaluation", () => {
    return deps.chessManager?.getEvaluationText() || "No position";
  });
  electron.ipcMain.handle("get-square-info", (_event, file, rank) => {
    return deps.chessManager?.getSquareInfo(file, rank) || null;
  });
  electron.ipcMain.handle("get-all-squares", () => {
    return deps.chessManager?.getAllSquareNames() || [];
  });
  electron.ipcMain.handle("start-realtime-monitoring", async (_event, playerColor) => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: "Real-time monitor not available" };
      }
      const result = await deps.realtimeMonitor.startMonitoring(playerColor);
      return result;
    } catch (error) {
      console.error("Error starting real-time monitoring:", error);
      return { success: false, error: "Failed to start monitoring" };
    }
  });
  electron.ipcMain.handle("stop-realtime-monitoring", () => {
    try {
      if (deps.realtimeMonitor) {
        deps.realtimeMonitor.stopMonitoring();
      }
      return { success: true };
    } catch (error) {
      console.error("Error stopping real-time monitoring:", error);
      return { success: false, error: "Failed to stop monitoring" };
    }
  });
  electron.ipcMain.handle("get-monitoring-state", () => {
    return deps.realtimeMonitor?.getState() || {
      isActive: false,
      website: null,
      playerColor: null,
      currentFen: null,
      lastMoveTime: 0,
      analysisCount: 0
    };
  });
  electron.ipcMain.handle("start-quick-mode", async (_event, playerColor) => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: "Real-time monitor not available" };
      }
      await deps.realtimeMonitor.startQuickMode(playerColor);
      return { success: true };
    } catch (error) {
      console.error("Error starting quick mode:", error);
      return { success: false, error: "Failed to start quick mode" };
    }
  });
  electron.ipcMain.handle("simulate-game", async () => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: "Real-time monitor not available" };
      }
      await deps.realtimeMonitor.simulateGame();
      return { success: true };
    } catch (error) {
      console.error("Error simulating game:", error);
      return { success: false, error: "Failed to simulate game" };
    }
  });
  electron.ipcMain.handle("analyze-manual-position", async (_event, fen) => {
    try {
      if (!deps.realtimeMonitor) {
        return { success: false, error: "Real-time monitor not available" };
      }
      await deps.realtimeMonitor.analyzeManualPosition(fen);
      return { success: true };
    } catch (error) {
      console.error("Error analyzing manual position:", error);
      return { success: false, error: "Failed to analyze position" };
    }
  });
  electron.ipcMain.handle("get-screenshots", async () => {
    try {
      let previews = [];
      const currentView = deps.getView();
      console.log("currentView", currentView);
      const queue = deps.getScreenshotQueue();
      previews = await Promise.all(
        queue.map(async (path2) => {
          const preview = await deps.getImagePreview(path2);
          return { path: path2, preview };
        })
      );
      return previews;
    } catch (error) {
      console.error("Error getting screenshots:", error);
      throw error;
    }
  });
  electron.ipcMain.handle("delete-screenshot", async (_, path2) => {
    return deps.deleteScreenshot(path2);
  });
  electron.ipcMain.handle("trigger-screenshot", async () => {
    const mainWindow = deps.getMainWindow();
    if (mainWindow) {
      try {
        const screenshotPath = await deps.takeScreenshot();
        const preview = await deps.getImagePreview(screenshotPath);
        mainWindow.webContents.send("screenshot-taken", { path: screenshotPath, preview });
        return { success: true };
      } catch (error) {
        console.error("Error triggering screenshot:", error);
        return { success: false, error: "Failed to take screenshot" };
      }
    }
    return { success: false, error: "Main window not found" };
  });
  electron.ipcMain.handle("toggle-main-window", async () => {
    return deps.toggleMainWindow();
  });
  electron.ipcMain.handle("delete-last-screenshot", async () => {
    try {
      const queue = deps.getView() === "queue" ? deps.getScreenshotQueue() : deps.getExtraScreenshotQueue();
      console.log("queue", queue);
      if (queue.length === 0) {
        return { success: false, error: "No screenshots to delete" };
      }
      const lastScreenshot = queue[queue.length - 1];
      const result = await deps.deleteScreenshot(lastScreenshot);
      const mainWindow = deps.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("screenshot-deleted");
      }
      return result;
    } catch (error) {
      console.error("Error deleting last screenshot:", error);
      return { success: false, error: "Failed to delete screenshot" };
    }
  });
  electron.ipcMain.handle("open-settings-portal", async () => {
    const mainWindow = deps.getMainWindow();
    if (mainWindow) {
      mainWindow.webContents.send("show-settings-dialog");
      return { success: true };
    }
    return { success: false, error: "Main window not found" };
  });
  electron.ipcMain.handle("trigger-chess-analysis", async () => {
    try {
      const mainWindow = deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send("chess-force-analysis");
      }
      return { success: true };
    } catch (error) {
      console.error("Error triggering chess analysis:", error);
      return { success: false, error: "Failed to trigger analysis" };
    }
  });
  electron.ipcMain.handle("trigger-reset", async () => {
    try {
      deps.chessManager?.stopAnalysis();
      deps.setIsAnalyzing(false);
      deps.clearQueues();
      deps.setView("analysis");
      const mainWindow = deps.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("chess-reset-view");
      }
      return { success: true };
    } catch (error) {
      console.error("Error triggering reset:", error);
      return { success: false, error: "Failed to reset" };
    }
  });
  electron.ipcMain.handle("set-window-dimensions", (_, width, height) => {
    return deps.setWindowDimensions(width, height);
  });
  electron.ipcMain.handle(
    "update-content-dimensions",
    async (_, { width, height }) => {
      if (width && height) {
        deps.setWindowDimensions(width, height);
      }
    }
  );
  electron.ipcMain.handle("openLink", (_, url) => {
    try {
      console.log("openLink", url);
      electron.shell.openExternal(url);
      return { success: true };
    } catch (error) {
      console.error("Error opening link:", error);
      return { success: false, error: "Failed to open link" };
    }
  });
}
class KeyboardShortcutHelper {
  deps;
  constructor(deps) {
    this.deps = deps;
  }
  adjustOpacity(delta) {
    const mainWindow = this.deps.getMainWindow();
    if (!mainWindow) return;
    const currentOpacity = mainWindow.getOpacity();
    const newOpacity = Math.max(0.1, Math.min(1, currentOpacity + delta));
    console.log("adjusting opacity", currentOpacity, newOpacity);
    mainWindow.setOpacity(newOpacity);
    try {
      const config = configManager.loadConfig();
      config.opacity = newOpacity;
      configManager.saveConfig(config);
    } catch (error) {
      console.error("Failed to save config:", error);
    }
  }
  registerGlobalShortcuts() {
    electron.globalShortcut.register("CommandOrControl+Left", () => {
      console.log("moveWindowLeft");
      this.deps.moveWindowLeft();
    });
    electron.globalShortcut.register("CommandOrControl+Right", () => {
      console.log("moveWindowRight");
      this.deps.moveWindowRight();
    });
    electron.globalShortcut.register("CommandOrControl+Up", () => {
      console.log("moveWindowUp");
      this.deps.moveWindowUp();
    });
    electron.globalShortcut.register("CommandOrControl+Down", () => {
      console.log("moveWindowDown");
      this.deps.moveWindowDown();
    });
    electron.globalShortcut.register("CommandOrControl+B", () => {
      console.log("toggleMainWindow");
      this.deps.toggleMainWindow();
    });
    electron.globalShortcut.register("CommandOrControl+H", async () => {
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        console.log("taking screenshot");
        try {
          const screenshotPath = await this.deps.takeScreenshot();
          const preview = await this.deps.getImagePreview(screenshotPath);
          console.log("screenshot taken", screenshotPath, preview);
          mainWindow.webContents.send("screenshot-taken", {
            path: screenshotPath,
            preview
          });
        } catch (error) {
          console.error("Failed to take screenshot:", error);
        }
      }
    });
    electron.globalShortcut.register("CommandOrControl+L", async () => {
      console.log("deleteLastScreenshot");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.send("screenshot-deleted");
      }
    });
    electron.globalShortcut.register("CommandOrControl+Enter", async () => {
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow && this.deps.chessManager) {
        console.log("Triggering chess analysis...");
        mainWindow.webContents.send("chess-force-analysis");
      }
    });
    electron.globalShortcut.register("CommandOrControl+[", () => {
      console.log("decreaseOpacity");
      this.adjustOpacity(-0.1);
    });
    electron.globalShortcut.register("CommandOrControl+]", () => {
      console.log("increaseOpacity");
      this.adjustOpacity(0.1);
    });
    electron.globalShortcut.register("CommandOrControl+-", () => {
      console.log("zoom out");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        const currentZoom = mainWindow.webContents.getZoomLevel();
        mainWindow.webContents.setZoomLevel(currentZoom - 0.5);
      }
    });
    electron.globalShortcut.register("CommandOrControl+=", () => {
      console.log("zoom in");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        const currentZoom = mainWindow.webContents.getZoomLevel();
        mainWindow.webContents.setZoomLevel(currentZoom + 0.5);
      }
    });
    electron.globalShortcut.register("CommandOrControl+0", () => {
      console.log("resetZoom");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow) {
        mainWindow.webContents.setZoomLevel(1);
      }
    });
    electron.globalShortcut.register("CommandOrControl+Q", () => {
      console.log("quit");
      electron.app.quit();
    });
    electron.globalShortcut.register("CommandOrControl+R", () => {
      console.log("Reset chess analysis");
      this.deps.chessManager?.stopAnalysis();
      this.deps.clearQueues();
      this.deps.setView("analysis");
      const mainWindow = this.deps.getMainWindow();
      if (mainWindow && !mainWindow.isDestroyed()) {
        mainWindow.webContents.send("chess-reset-view");
      }
    });
  }
}
const execFileAsync = util.promisify(child_process.execFile);
class ScreenshotManager {
  screenshotQueue = [];
  extraScreenshotQueue = [];
  MAX_SCREENSHOTS = 5;
  screenshotDir;
  extraScreenshotDir;
  tempDir;
  view = "queue";
  constructor(view = "queue") {
    this.view = view;
    this.screenshotDir = path.join(electron.app.getPath("userData"), "screenshots");
    this.extraScreenshotDir = path.join(electron.app.getPath("userData"), "extra_screenshots");
    this.tempDir = path.join(electron.app.getPath("temp"), "cluelessly-screenshots");
    this.ensureDirectoriesExist();
    this.cleanScreenshotDirectories();
  }
  ensureDirectoriesExist() {
    const directories = [this.screenshotDir, this.extraScreenshotDir, this.tempDir];
    for (const dir of directories) {
      if (!fs.existsSync(dir)) {
        try {
          fs.mkdirSync(dir, { recursive: true });
          console.log(`Created directory: ${dir}`);
        } catch (error) {
          console.error(`Failed to create directory ${dir}:`, error);
        }
      }
    }
  }
  cleanScreenshotDirectories() {
    try {
      if (fs.existsSync(this.screenshotDir)) {
        const files = fs.readdirSync(this.screenshotDir).filter((file) => file.endsWith(".png")).map((file) => path.join(this.screenshotDir, file));
        for (const file of files) {
          try {
            fs.unlinkSync(file);
            console.log(`Deleted screenshot file: ${file}`);
          } catch (error) {
            console.error(`Failed to delete file ${file}:`, error);
          }
        }
      }
      if (fs.existsSync(this.extraScreenshotDir)) {
        const files = fs.readdirSync(this.extraScreenshotDir).filter((file) => file.endsWith(".png")).map((file) => path.join(this.extraScreenshotDir, file));
        for (const file of files) {
          try {
            fs.unlinkSync(file);
            console.log(`Deleted extra screenshot file: ${file}`);
          } catch (error) {
            console.error(`Failed to delete file ${file}:`, error);
          }
        }
      }
      console.log("Screenshot directories cleaned successfully");
    } catch (error) {
      console.error("Failed to clean screenshot directories:", error);
    }
  }
  getView() {
    return this.view;
  }
  setView(view) {
    this.view = view;
  }
  getScreenshotQueue() {
    return this.screenshotQueue;
  }
  getExtraScreenshotQueue() {
    return this.extraScreenshotQueue;
  }
  clearQueues() {
    this.screenshotQueue.forEach((file) => {
      try {
        fs.unlinkSync(file);
        console.log(`Deleted screenshot file: ${file}`);
      } catch (error) {
        console.error(`Failed to delete file ${file}:`, error);
      }
    });
    this.screenshotQueue = [];
    this.extraScreenshotQueue.forEach((file) => {
      try {
        fs.unlinkSync(file);
        console.log(`Deleted extra screenshot file: ${file}`);
      } catch (error) {
        console.error(`Failed to delete file ${file}:`, error);
      }
    });
    this.extraScreenshotQueue = [];
    console.log("Screenshot queues cleared successfully");
  }
  async captureScreenshot() {
    try {
      console.log("Starting screenshot capture...");
      if (process.platform === "win32") {
        return await this.captureWindowsScreenshot();
      }
      console.log("Taking screenshot on non-Windows platform...");
      const buffer = await screenshot({
        format: "png"
      });
      console.log("Screenshot captured successfully");
      return buffer;
    } catch (error) {
      console.error("Failed to capture screenshot:", error);
      throw error;
    }
  }
  async captureWindowsScreenshot() {
    try {
      console.log("Starting Windows screenshot capture...");
      const tempFilePath = path.join(this.tempDir, `temp-${uuid.v4()}.png`);
      await screenshot({
        path: tempFilePath
      });
      if (fs.existsSync(tempFilePath)) {
        const buffer = await fs.promises.readFile(tempFilePath);
        try {
          await fs.promises.unlink(tempFilePath);
        } catch (error) {
          console.error("Failed to delete temp file:", error);
        }
        console.log("Screenshot captured successfully");
        return buffer;
      } else {
        console.error("Failed to capture screenshot: Temp file not found");
        throw new Error("Failed to capture screenshot");
      }
    } catch (error) {
      console.error("Failed to capture Windows screenshot:", error);
      try {
        console.log("Trying powrshell method");
        const tempFilePath = path.join(this.tempDir, `temp-${uuid.v4()}.png`);
        const psScript = `
         Add-Type -AssemblyName System.Windows.Forms,System.Drawing
         $screens = [System.Windows.Forms.Screen]::AllScreens
         $top = ($screens | ForEach-Object {$_.Bounds.Top} | Measure-Object -Minimum).Minimum
         $left = ($screens | ForEach-Object {$_.Bounds.Left} | Measure-Object -Minimum).Minimum
         $width = ($screens | ForEach-Object {$_.Bounds.Right} | Measure-Object -Maximum).Maximum
         $height = ($screens | ForEach-Object {$_.Bounds.Bottom} | Measure-Object -Maximum).Maximum
         $bounds = [System.Drawing.Rectangle]::FromLTRB($left, $top, $width, $height)
         $bmp = New-Object System.Drawing.Bitmap $bounds.Width, $bounds.Height
         $graphics = [System.Drawing.Graphics]::FromImage($bmp)
         $graphics.CopyFromScreen($bounds.Left, $bounds.Top, 0, 0, $bounds.Size)
         $bmp.Save('${tempFilePath.replace(/\\/g, "\\\\")}', [System.Drawing.Imaging.ImageFormat]::Png)
         $graphics.Dispose()
         $bmp.Dispose()
         `;
        await execFileAsync("powershell", [
          "-NoProfile",
          "-ExecutionPolicy",
          "Bypass",
          "-Command",
          psScript
        ]);
        if (fs.existsSync(tempFilePath)) {
          const buffer = await fs.promises.readFile(tempFilePath);
          try {
            await fs.promises.unlink(tempFilePath);
          } catch (error2) {
            console.error("Failed to delete temp file:", error2);
          }
          console.log("Screenshot captured successfully");
          return buffer;
        } else {
          console.error("Failed to capture screenshot: Temp file not found");
          throw new Error("Failed to capture screenshot");
        }
      } catch (error2) {
        console.error("Failed to capture screenshot using PowerShell:", error2);
        throw error2;
      }
    }
  }
  async takeScreenshot(hideMainWindow2, showMainWindow2) {
    console.log("Taking screenshot in view", this.view);
    hideMainWindow2();
    const hideDelay = process.platform === "win32" ? 500 : 300;
    await new Promise((resolve) => setTimeout(resolve, hideDelay));
    let screenshotPath = "";
    try {
      const screenshotBuffer = await this.captureScreenshot();
      if (!screenshotBuffer || screenshotBuffer.length === 0) {
        throw new Error("Failed to capture screenshot");
      }
      if (this.view === "queue") {
        screenshotPath = path.join(this.screenshotDir, `screenshot-${uuid.v4()}.png`);
        await fs.promises.writeFile(screenshotPath, screenshotBuffer);
        console.log(`Screenshot saved to ${screenshotPath}`);
        this.screenshotQueue.push(screenshotPath);
        if (this.screenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.screenshotQueue.shift();
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath);
              console.log(`Deleted old screenshot file: ${removedPath}`);
            } catch (error) {
              console.error(`Failed to delete file ${removedPath}:`, error);
            }
          }
        }
      } else {
        screenshotPath = path.join(this.extraScreenshotDir, `screenshot-${uuid.v4()}.png`);
        await fs.promises.writeFile(screenshotPath, screenshotBuffer);
        console.log(`Screenshot saved to ${screenshotPath}`);
        this.extraScreenshotQueue.push(screenshotPath);
        if (this.extraScreenshotQueue.length > this.MAX_SCREENSHOTS) {
          const removedPath = this.extraScreenshotQueue.shift();
          if (removedPath) {
            try {
              await fs.promises.unlink(removedPath);
              console.log(`Deleted old screenshot file: ${removedPath}`);
            } catch (error) {
              console.error(`Failed to delete file ${removedPath}:`, error);
            }
          }
        }
      }
    } catch (error) {
      console.error("Failed to take screenshot:", error);
      throw error;
    } finally {
      await new Promise((resolve) => setTimeout(resolve, 200));
      showMainWindow2();
    }
    return screenshotPath;
  }
  async getImagePreview(filePath) {
    try {
      if (!fs.existsSync(filePath)) {
        console.error(`File does not exist: ${filePath}`);
        return "";
      }
      const data = await fs.promises.readFile(filePath);
      const base64 = data.toString("base64");
      return `data:image/png;base64,${base64}`;
    } catch (error) {
      console.error("Failed to get image preview:", error);
      return "";
    }
  }
  async deleteScreenshot(path2) {
    try {
      if (fs.existsSync(path2)) {
        await fs.promises.unlink(path2);
      }
      if (this.view === "queue") {
        this.screenshotQueue = this.screenshotQueue.filter((p) => p !== path2);
      } else if (this.view === "solutions") {
        this.extraScreenshotQueue = this.extraScreenshotQueue.filter((p) => p !== path2);
      }
      return { success: true };
    } catch (error) {
      console.error("Failed to delete screenshot:", error);
      return { success: false, error: error instanceof Error ? error.message : "Unknown error" };
    }
  }
  clearExtraScreenshotQueue() {
    this.extraScreenshotQueue.forEach((path2) => {
      if (fs.existsSync(path2)) {
        fs.unlink(path2, (err) => {
          if (err) {
            console.error(`Failed to delete extra screenshot file: ${path2}`, err);
          }
        });
      }
    });
    this.extraScreenshotQueue = [];
  }
}
class SimpleChess {
  fenString = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
  load(fen) {
    const parts = fen.split(" ");
    if (parts.length !== 6) return false;
    const board = parts[0];
    const ranks = board.split("/");
    if (ranks.length !== 8) return false;
    for (const rank of ranks) {
      let squares = 0;
      for (const char of rank) {
        if ("12345678".includes(char)) {
          squares += parseInt(char);
        } else if ("rnbqkpRNBQKP".includes(char)) {
          squares += 1;
        } else {
          return false;
        }
      }
      if (squares !== 8) return false;
    }
    this.fenString = fen;
    return true;
  }
  fen() {
    return this.fenString;
  }
  move(moveStr) {
    if (typeof moveStr === "string" && moveStr.length >= 4) {
      return {
        from: moveStr.substring(0, 2),
        to: moveStr.substring(2, 4),
        promotion: moveStr.length > 4 ? moveStr[4] : void 0,
        san: moveStr
        // Simplified
      };
    }
    return null;
  }
  undo() {
  }
}
class ChessEngine {
  stockfish = null;
  chess;
  isReady = false;
  analysisCallback = null;
  constructor() {
    this.chess = new SimpleChess();
    this.initializeEngine();
  }
  async initializeEngine() {
    try {
      this.fallbackToMockEngine();
    } catch (error) {
      console.error("Error initializing chess engine:", error);
      this.fallbackToMockEngine();
    }
  }
  fallbackToMockEngine() {
    console.log("Using mock chess engine");
    this.isReady = true;
    setTimeout(() => {
      console.log("Mock chess engine ready");
    }, 500);
  }
  sendCommand(command) {
    if (this.stockfish && this.stockfish.stdin) {
      this.stockfish.stdin.write(command + "\n");
    }
  }
  handleEngineOutput(output) {
    const lines = output.trim().split("\n");
    for (const line of lines) {
      if (line.includes("uciok")) {
        this.sendCommand("setoption name Threads value 1");
        this.sendCommand("setoption name Hash value 128");
      } else if (line.includes("readyok")) {
        this.isReady = true;
        console.log("Chess engine ready");
      } else if (line.startsWith("bestmove")) {
        this.handleBestMove(line);
      } else if (line.startsWith("info")) {
        this.handleAnalysisInfo(line);
      }
    }
  }
  handleBestMove(line) {
    const parts = line.split(" ");
    const bestMoveUci = parts[1];
    if (bestMoveUci && bestMoveUci !== "(none)") {
      try {
        const move = this.chess.move(bestMoveUci);
        if (move && this.analysisCallback) {
          const analysis = {
            bestMove: {
              from: move.from,
              to: move.to,
              promotion: move.promotion,
              san: move.san,
              uci: bestMoveUci
            },
            evaluation: 0,
            depth: 0,
            pv: [bestMoveUci]
          };
          this.analysisCallback(analysis);
        }
        this.chess.undo();
      } catch (error) {
        console.error("Error processing best move:", error);
      }
    }
  }
  handleAnalysisInfo(line) {
    const parts = line.split(" ");
    let depth = 0;
    let evaluation = 0;
    let pv = [];
    let mate;
    for (let i = 0; i < parts.length; i++) {
      if (parts[i] === "depth") {
        depth = parseInt(parts[i + 1]) || 0;
      } else if (parts[i] === "cp") {
        evaluation = parseInt(parts[i + 1]) || 0;
      } else if (parts[i] === "mate") {
        mate = parseInt(parts[i + 1]) || 0;
      } else if (parts[i] === "pv") {
        pv = parts.slice(i + 1);
        break;
      }
    }
    if (depth >= 10 && this.analysisCallback) {
      const analysis = {
        bestMove: null,
        evaluation: evaluation / 100,
        // Convert centipawns to pawns
        depth,
        pv,
        mate
      };
      this.analysisCallback(analysis);
    }
  }
  setPosition(fen) {
    try {
      this.chess.load(fen);
      if (this.isReady) {
        this.sendCommand(`position fen ${fen}`);
      }
      return true;
    } catch (error) {
      console.error("Invalid FEN:", error);
      return false;
    }
  }
  analyzePosition(callback, depth = 15) {
    if (!this.isReady) {
      console.error("Chess engine not ready");
      return;
    }
    this.analysisCallback = callback;
    setTimeout(() => {
      const mockMoves = ["e2e4", "e7e5", "g1f3", "b8c6", "d2d4", "d7d6"];
      const randomMove = mockMoves[Math.floor(Math.random() * mockMoves.length)];
      const move = this.chess.move(randomMove);
      if (move && this.analysisCallback) {
        const analysis = {
          bestMove: {
            from: move.from,
            to: move.to,
            promotion: move.promotion,
            san: move.san,
            uci: randomMove
          },
          evaluation: (Math.random() - 0.5) * 4,
          // Random evaluation between -2 and +2
          depth,
          pv: [randomMove]
        };
        this.analysisCallback(analysis);
      }
      this.chess.undo();
    }, 1e3 + Math.random() * 2e3);
  }
  stopAnalysis() {
    if (this.isReady) {
      this.sendCommand("stop");
    }
    this.analysisCallback = null;
  }
  getCurrentPosition() {
    return this.chess.fen();
  }
  isValidMove(from, to, promotion) {
    try {
      const move = this.chess.move({ from, to, promotion });
      if (move) {
        this.chess.undo();
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }
  destroy() {
    this.stopAnalysis();
    if (this.stockfish) {
      this.stockfish.kill();
      this.stockfish = null;
    }
  }
}
class ChessBoardDetector {
  PIECE_PATTERNS = {
    // Chess.com piece patterns (simplified color detection)
    white: {
      king: { r: [200, 255], g: [200, 255], b: [200, 255] },
      queen: { r: [200, 255], g: [200, 255], b: [200, 255] },
      rook: { r: [200, 255], g: [200, 255], b: [200, 255] },
      bishop: { r: [200, 255], g: [200, 255], b: [200, 255] },
      knight: { r: [200, 255], g: [200, 255], b: [200, 255] },
      pawn: { r: [200, 255], g: [200, 255], b: [200, 255] }
    },
    black: {
      king: { r: [0, 100], g: [0, 100], b: [0, 100] },
      queen: { r: [0, 100], g: [0, 100], b: [0, 100] },
      rook: { r: [0, 100], g: [0, 100], b: [0, 100] },
      bishop: { r: [0, 100], g: [0, 100], b: [0, 100] },
      knight: { r: [0, 100], g: [0, 100], b: [0, 100] },
      pawn: { r: [0, 100], g: [0, 100], b: [0, 100] }
    }
  };
  async detectChessBoard(imagePath) {
    try {
      console.log("Mock chess board detection for:", imagePath);
      if (!fs.existsSync(imagePath)) {
        console.error("Screenshot file does not exist:", imagePath);
        return null;
      }
      const boardBounds = {
        x: 100,
        y: 100,
        width: 480,
        height: 480
      };
      const squares = this.generateMockSquares(boardBounds);
      const detectedSquares = this.generateStartingPosition(squares);
      const fen = this.generateFEN(detectedSquares, "white");
      return {
        squares: detectedSquares,
        fen,
        boardBounds,
        playerColor: "white",
        confidence: 0.85
        // Mock confidence
      };
    } catch (error) {
      console.error("Error detecting chess board:", error);
      return null;
    }
  }
  generateMockSquares(boardBounds) {
    const squares = [];
    const squareWidth = boardBounds.width / 8;
    const squareHeight = boardBounds.height / 8;
    for (let file = 0; file < 8; file++) {
      for (let rank = 0; rank < 8; rank++) {
        const x = boardBounds.x + file * squareWidth;
        const y = boardBounds.y + rank * squareHeight;
        squares.push({
          file: String.fromCharCode(97 + file),
          // 'a' to 'h'
          rank: 8 - rank,
          // 8 to 1 (from top to bottom)
          piece: null,
          x: Math.floor(x),
          y: Math.floor(y),
          width: Math.floor(squareWidth),
          height: Math.floor(squareHeight)
        });
      }
    }
    return squares;
  }
  generateStartingPosition(squares) {
    const detectedSquares = [...squares];
    const startingPieces = {
      "a8": "r",
      "b8": "n",
      "c8": "b",
      "d8": "q",
      "e8": "k",
      "f8": "b",
      "g8": "n",
      "h8": "r",
      "a7": "p",
      "b7": "p",
      "c7": "p",
      "d7": "p",
      "e7": "p",
      "f7": "p",
      "g7": "p",
      "h7": "p",
      "a2": "P",
      "b2": "P",
      "c2": "P",
      "d2": "P",
      "e2": "P",
      "f2": "P",
      "g2": "P",
      "h2": "P",
      "a1": "R",
      "b1": "N",
      "c1": "B",
      "d1": "Q",
      "e1": "K",
      "f1": "B",
      "g1": "N",
      "h1": "R"
    };
    for (const square of detectedSquares) {
      const squareName = `${square.file}${square.rank}`;
      square.piece = startingPieces[squareName] || null;
    }
    return detectedSquares;
  }
  determinePlayerColor(squares) {
    const whiteKing = squares.find((s) => s.piece === "K");
    const blackKing = squares.find((s) => s.piece === "k");
    if (whiteKing && whiteKing.rank === 1) {
      return "white";
    } else if (blackKing && blackKing.rank === 1) {
      return "black";
    }
    return "white";
  }
  generateFEN(squares, playerColor) {
    let fen = "";
    for (let rank = 8; rank >= 1; rank--) {
      let emptyCount = 0;
      for (let file = 0; file < 8; file++) {
        const square = squares.find(
          (s) => s.file === String.fromCharCode(97 + file) && s.rank === rank
        );
        if (square?.piece) {
          if (emptyCount > 0) {
            fen += emptyCount.toString();
            emptyCount = 0;
          }
          fen += square.piece;
        } else {
          emptyCount++;
        }
      }
      if (emptyCount > 0) {
        fen += emptyCount.toString();
      }
      if (rank > 1) {
        fen += "/";
      }
    }
    fen += ` ${playerColor === "white" ? "w" : "b"} KQkq - 0 1`;
    return fen;
  }
  calculateConfidence(squares) {
    const occupiedSquares = squares.filter((s) => s.piece !== null).length;
    if (occupiedSquares >= 16 && occupiedSquares <= 32) {
      return Math.min(0.9, occupiedSquares / 32);
    } else if (occupiedSquares > 0) {
      return Math.max(0.3, occupiedSquares / 32);
    }
    return 0.1;
  }
}
class ChessManager {
  engine;
  detector;
  currentPosition = null;
  analysisCallback = null;
  positionCallback = null;
  isAnalyzing = false;
  constructor() {
    this.engine = new ChessEngine();
    this.detector = new ChessBoardDetector();
  }
  async analyzeScreenshot(screenshotPath, onAnalysis, onPosition) {
    try {
      console.log("Analyzing chess screenshot:", screenshotPath);
      const boardDetection = await this.detector.detectChessBoard(screenshotPath);
      if (!boardDetection) {
        console.log("No chess board detected in screenshot");
        return null;
      }
      console.log("Chess board detected with confidence:", boardDetection.confidence);
      console.log("Detected FEN:", boardDetection.fen);
      return await this.analyzePosition(boardDetection.fen, "w", onAnalysis, onPosition, boardDetection);
    } catch (error) {
      console.error("Error analyzing chess screenshot:", error);
      return null;
    }
  }
  async analyzePosition(fen, playerColor = "w", onAnalysis, onPosition, boardDetection) {
    try {
      console.log("Analyzing chess position:", fen);
      const position = {
        fen,
        boardDetection: boardDetection || {
          squares: [],
          fen,
          boardBounds: { x: 0, y: 0, width: 480, height: 480 },
          playerColor: playerColor === "w" ? "white" : "black",
          confidence: 1
        },
        timestamp: Date.now()
      };
      this.analysisCallback = onAnalysis || null;
      this.positionCallback = onPosition || null;
      const validPosition = this.engine.setPosition(fen);
      if (!validPosition) {
        console.error("Invalid chess position:", fen);
        return null;
      }
      this.currentPosition = position;
      if (this.positionCallback) {
        this.positionCallback(position);
      }
      this.isAnalyzing = true;
      this.engine.analyzePosition((analysis) => {
        if (this.currentPosition) {
          this.currentPosition.analysis = analysis;
          if (this.analysisCallback) {
            this.analysisCallback(analysis);
          }
        }
      });
      return position;
    } catch (error) {
      console.error("Error analyzing chess position:", error);
      return null;
    }
  }
  getCurrentPosition() {
    return this.currentPosition;
  }
  getBestMoveHighlight() {
    if (!this.currentPosition?.analysis?.bestMove || !this.currentPosition.boardDetection) {
      return null;
    }
    const { bestMove } = this.currentPosition.analysis;
    const { squares } = this.currentPosition.boardDetection;
    const fromSquare = squares.find(
      (s) => s.file === bestMove.from[0] && s.rank === parseInt(bestMove.from[1])
    );
    const toSquare = squares.find(
      (s) => s.file === bestMove.to[0] && s.rank === parseInt(bestMove.to[1])
    );
    if (!fromSquare || !toSquare) {
      return null;
    }
    return {
      from: {
        x: fromSquare.x,
        y: fromSquare.y,
        width: fromSquare.width,
        height: fromSquare.height
      },
      to: {
        x: toSquare.x,
        y: toSquare.y,
        width: toSquare.width,
        height: toSquare.height
      },
      move: bestMove
    };
  }
  getSquareInfo(file, rank) {
    if (!this.currentPosition?.boardDetection) {
      return null;
    }
    const square = this.currentPosition.boardDetection.squares.find(
      (s) => s.file === file && s.rank === rank
    );
    if (!square) {
      return null;
    }
    return {
      name: `${file}${rank}`,
      piece: square.piece
    };
  }
  getAllSquareNames() {
    const squares = [];
    for (let file = 0; file < 8; file++) {
      for (let rank = 1; rank <= 8; rank++) {
        squares.push(`${String.fromCharCode(97 + file)}${rank}`);
      }
    }
    return squares;
  }
  getEvaluationText() {
    if (!this.currentPosition?.analysis) {
      return "Analyzing...";
    }
    const { analysis } = this.currentPosition;
    if (analysis.mate !== void 0) {
      const mateIn = Math.abs(analysis.mate);
      const side = analysis.mate > 0 ? "White" : "Black";
      return `${side} mates in ${mateIn}`;
    }
    const evaluation = analysis.evaluation;
    if (Math.abs(evaluation) < 0.1) {
      return "Equal position";
    } else if (evaluation > 0) {
      return `White +${evaluation.toFixed(1)}`;
    } else {
      return `Black +${Math.abs(evaluation).toFixed(1)}`;
    }
  }
  getBestMoveText() {
    if (!this.currentPosition?.analysis?.bestMove) {
      return "Calculating...";
    }
    const { bestMove } = this.currentPosition.analysis;
    return `Best move: ${bestMove.san} (${bestMove.from}-${bestMove.to})`;
  }
  stopAnalysis() {
    this.isAnalyzing = false;
    this.engine.stopAnalysis();
    this.analysisCallback = null;
    this.positionCallback = null;
  }
  isCurrentlyAnalyzing() {
    return this.isAnalyzing;
  }
  sendMoveHighlightToRenderer(mainWindow) {
    const highlight = this.getBestMoveHighlight();
    if (highlight && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("chess-move-highlight", {
        highlight,
        evaluation: this.getEvaluationText(),
        bestMove: this.getBestMoveText(),
        position: this.currentPosition
      });
    }
  }
  sendAnalysisToRenderer(mainWindow) {
    if (this.currentPosition && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send("chess-analysis-update", {
        position: this.currentPosition,
        evaluation: this.getEvaluationText(),
        bestMove: this.getBestMoveText(),
        highlight: this.getBestMoveHighlight()
      });
    }
  }
  clearPosition() {
    this.stopAnalysis();
    this.currentPosition = null;
  }
  destroy() {
    this.stopAnalysis();
    this.engine.destroy();
  }
  // Utility methods for chess notation
  static squareToCoordinates(square) {
    if (square.length !== 2) return null;
    const file = square[0];
    const rank = parseInt(square[1]);
    if (file < "a" || file > "h" || rank < 1 || rank > 8) {
      return null;
    }
    return { file, rank };
  }
  static coordinatesToSquare(file, rank) {
    if (file < "a" || file > "h" || rank < 1 || rank > 8) {
      return null;
    }
    return `${file}${rank}`;
  }
  static isValidSquare(square) {
    return this.squareToCoordinates(square) !== null;
  }
  // Get piece symbol for display
  static getPieceSymbol(piece) {
    const symbols = {
      "K": "♔",
      "Q": "♕",
      "R": "♖",
      "B": "♗",
      "N": "♘",
      "P": "♙",
      "k": "♚",
      "q": "♛",
      "r": "♜",
      "b": "♝",
      "n": "♞",
      "p": "♟"
    };
    return symbols[piece] || piece;
  }
  // Get piece name for display
  static getPieceName(piece) {
    const names = {
      "K": "White King",
      "Q": "White Queen",
      "R": "White Rook",
      "B": "White Bishop",
      "N": "White Knight",
      "P": "White Pawn",
      "k": "Black King",
      "q": "Black Queen",
      "r": "Black Rook",
      "b": "Black Bishop",
      "n": "Black Knight",
      "p": "Black Pawn"
    };
    return names[piece] || "Unknown piece";
  }
}
class BrowserAutomation {
  isMonitoring = false;
  currentWebContents = null;
  playerColor = "w";
  lastFen = "";
  monitorInterval = null;
  chessManager;
  onPositionChange;
  onError;
  // Chess website configurations
  websites = [
    {
      name: "Chess.com",
      domains: ["chess.com", "www.chess.com"],
      boardSelector: ".board",
      pieceSelector: ".piece",
      highlightContainer: "wc-chess-board",
      getFenScript: this.getChessComFenScript(),
      injectHighlightScript: this.getChessComHighlightScript()
    },
    {
      name: "Lichess",
      domains: ["lichess.org", "www.lichess.org"],
      boardSelector: "cg-board",
      pieceSelector: "piece",
      highlightContainer: "cg-board",
      getFenScript: this.getLichessFenScript(),
      injectHighlightScript: this.getLichessHighlightScript()
    }
  ];
  constructor(chessManager) {
    this.chessManager = chessManager;
  }
  async startMonitoring(playerColor, onPositionChange, onError) {
    try {
      if (this.isMonitoring) {
        return { success: false, error: "Already monitoring" };
      }
      this.playerColor = playerColor === "white" ? "w" : "b";
      this.onPositionChange = onPositionChange;
      this.onError = onError;
      const result = await this.findChessWebsite();
      if (!result.success) {
        return result;
      }
      this.isMonitoring = true;
      this.startPositionMonitoring();
      console.log(`Started monitoring chess website as ${playerColor}`);
      return { success: true };
    } catch (error) {
      console.error("Error starting browser automation:", error);
      return { success: false, error: "Failed to start monitoring" };
    }
  }
  stopMonitoring() {
    this.isMonitoring = false;
    if (this.monitorInterval) {
      clearInterval(this.monitorInterval);
      this.monitorInterval = null;
    }
    this.currentWebContents = null;
    this.lastFen = "";
    console.log("Stopped browser monitoring");
  }
  async findChessWebsite() {
    try {
      const allWebContents = electron.webContents.getAllWebContents();
      for (const wc of allWebContents) {
        try {
          const url = wc.getURL();
          const website = this.websites.find(
            (site) => site.domains.some((domain) => url.includes(domain))
          );
          if (website) {
            const hasBoardResult = await wc.executeJavaScript(`
              (function() {
                try {
                  const board = document.querySelector('${website.boardSelector}');
                  return board !== null;
                } catch (e) {
                  return false;
                }
              })()
            `);
            if (hasBoardResult) {
              this.currentWebContents = wc;
              console.log(`Found ${website.name} with chess board`);
              return { success: true };
            }
          }
        } catch (error) {
          continue;
        }
      }
      return {
        success: false,
        error: "No chess website found. Please open chess.com or lichess.org in your browser and start a game."
      };
    } catch (error) {
      return { success: false, error: "Failed to find chess website" };
    }
  }
  startPositionMonitoring() {
    this.monitorInterval = setInterval(async () => {
      if (!this.isMonitoring || !this.currentWebContents) {
        return;
      }
      try {
        const fen = await this.getCurrentFen();
        if (fen && fen !== this.lastFen) {
          this.lastFen = fen;
          console.log("Position changed:", fen);
          if (this.onPositionChange) {
            this.onPositionChange(fen);
          }
          await this.chessManager.analyzePosition(fen, this.playerColor);
        }
      } catch (error) {
        console.error("Error monitoring position:", error);
        if (this.onError) {
          this.onError("Failed to read chess position");
        }
      }
    }, 1e3);
  }
  async getCurrentFen() {
    if (!this.currentWebContents) {
      return null;
    }
    try {
      const url = this.currentWebContents.getURL();
      const website = this.websites.find(
        (site) => site.domains.some((domain) => url.includes(domain))
      );
      if (!website) {
        return null;
      }
      const fen = await this.currentWebContents.executeJavaScript(website.getFenScript);
      return fen ? `${fen} ${this.playerColor}` : null;
    } catch (error) {
      console.error("Error getting FEN:", error);
      return null;
    }
  }
  async highlightBestMove(from, to) {
    if (!this.currentWebContents || !this.isMonitoring) {
      return;
    }
    try {
      const url = this.currentWebContents.getURL();
      const website = this.websites.find(
        (site) => site.domains.some((domain) => url.includes(domain))
      );
      if (!website) {
        return;
      }
      await this.currentWebContents.executeJavaScript(`
        ${website.injectHighlightScript}
        highlightMove('${from}', '${to}');
      `);
    } catch (error) {
      console.error("Error highlighting move:", error);
    }
  }
  getChessComFenScript() {
    return `
      (function() {
        try {
          function getFenString() {
            let fen_string = "";
            for(var i = 8; i >= 1; i--) {
              for(var j = 1; j <= 8; j++) {
                let position = \`\${j}\${i}\`;
                if(j == 1 && i != 8) {
                  fen_string += "/";
                }
                let piece_in_position = document.querySelectorAll(\`.piece.square-\${position}\`)[0]?.classList ?? null;
                if(piece_in_position != null) {
                  for(var item of piece_in_position.values()) {
                    if(item.length == 2) {
                      piece_in_position = item;
                    }
                  }
                }
                if(piece_in_position == null) {
                  let previous_char = fen_string.split("").pop();
                  if(!isNaN(Number(previous_char))) {
                    fen_string = fen_string.substring(0, fen_string.length - 1);
                    fen_string += Number(previous_char) + 1;
                  } else {
                    fen_string += "1";
                  }
                } else if(piece_in_position?.split("")[0] == "b") {
                  fen_string += piece_in_position.split("")[1];
                } else if(piece_in_position?.split("")[0] == "w") {
                  fen_string += piece_in_position.split("")[1].toUpperCase();
                }
              }
            }
            return fen_string;
          }
          return getFenString();
        } catch (e) {
          return null;
        }
      })()
    `;
  }
  getChessComHighlightScript() {
    return `
      function highlightMove(from, to) {
        try {
          // Remove previous highlights
          document.querySelectorAll(".chessly-highlight").forEach(el => el.remove());
          
          const char_map = {"a":1,"b":2,"c":3,"d":4,"e":5,"f":6,"g":7,"h":8};
          const fromSquare = \`\${char_map[from[0]]}\${from[1]}\`;
          const toSquare = \`\${char_map[to[0]]}\${to[1]}\`;
          
          // Create highlight elements
          const fromHighlight = document.createElement("div");
          fromHighlight.className = \`highlight chessly-highlight square-\${fromSquare}\`;
          fromHighlight.style = "background: #ff6b6b; opacity: 0.7; pointer-events: none; z-index: 10;";
          
          const toHighlight = document.createElement("div");
          toHighlight.className = \`highlight chessly-highlight square-\${toSquare}\`;
          toHighlight.style = "background: #51cf66; opacity: 0.7; pointer-events: none; z-index: 10;";
          
          const board = document.querySelector("wc-chess-board") || document.querySelector(".board");
          if (board) {
            board.appendChild(fromHighlight);
            board.appendChild(toHighlight);
          }
        } catch (e) {
          console.error("Error highlighting move:", e);
        }
      }
    `;
  }
  getLichessFenScript() {
    return `
      (function() {
        try {
          // Lichess FEN extraction - more complex due to different DOM structure
          const pieces = document.querySelectorAll('cg-board piece');
          const board = {};
          
          pieces.forEach(piece => {
            const pos = piece.getAttribute('cgKey');
            const role = piece.getAttribute('cgRole');
            const color = piece.getAttribute('cgColor');
            
            if (pos && role && color) {
              const file = pos[0];
              const rank = parseInt(pos[1]);
              const pieceChar = color === 'white' ? role[0].toUpperCase() : role[0];
              board[pos] = pieceChar;
            }
          });
          
          let fen = '';
          for (let rank = 8; rank >= 1; rank--) {
            let emptyCount = 0;
            for (let file of 'abcdefgh') {
              const square = file + rank;
              if (board[square]) {
                if (emptyCount > 0) {
                  fen += emptyCount;
                  emptyCount = 0;
                }
                fen += board[square];
              } else {
                emptyCount++;
              }
            }
            if (emptyCount > 0) {
              fen += emptyCount;
            }
            if (rank > 1) {
              fen += '/';
            }
          }
          
          return fen;
        } catch (e) {
          return null;
        }
      })()
    `;
  }
  getLichessHighlightScript() {
    return `
      function highlightMove(from, to) {
        try {
          // Remove previous highlights
          document.querySelectorAll(".chessly-highlight").forEach(el => el.remove());
          
          // Create highlight elements for Lichess
          const board = document.querySelector('cg-board');
          if (!board) return;
          
          const fromEl = document.createElement('div');
          fromEl.className = 'chessly-highlight';
          fromEl.style = \`
            position: absolute;
            background: rgba(255, 107, 107, 0.7);
            width: 12.5%;
            height: 12.5%;
            pointer-events: none;
            z-index: 10;
            left: \${(from.charCodeAt(0) - 97) * 12.5}%;
            bottom: \${(parseInt(from[1]) - 1) * 12.5}%;
          \`;
          
          const toEl = document.createElement('div');
          toEl.className = 'chessly-highlight';
          toEl.style = \`
            position: absolute;
            background: rgba(81, 207, 102, 0.7);
            width: 12.5%;
            height: 12.5%;
            pointer-events: none;
            z-index: 10;
            left: \${(to.charCodeAt(0) - 97) * 12.5}%;
            bottom: \${(parseInt(to[1]) - 1) * 12.5}%;
          \`;
          
          board.appendChild(fromEl);
          board.appendChild(toEl);
        } catch (e) {
          console.error("Error highlighting move:", e);
        }
      }
    `;
  }
  isCurrentlyMonitoring() {
    return this.isMonitoring;
  }
  getCurrentWebsite() {
    if (!this.currentWebContents) {
      return null;
    }
    const url = this.currentWebContents.getURL();
    const website = this.websites.find(
      (site) => site.domains.some((domain) => url.includes(domain))
    );
    return website?.name || null;
  }
}
class RealtimeMonitor {
  chessManager;
  browserAutomation;
  mainWindow = null;
  state = {
    isActive: false,
    website: null,
    playerColor: null,
    currentFen: null,
    lastMoveTime: 0,
    analysisCount: 0
  };
  constructor(chessManager, options = {}) {
    this.chessManager = chessManager;
    this.browserAutomation = new BrowserAutomation(this.chessManager);
  }
  setMainWindow(window) {
    this.mainWindow = window;
  }
  async startMonitoring(playerColor = "white") {
    try {
      if (this.state.isActive) {
        return { success: false, error: "Monitoring already active" };
      }
      console.log("🎯 Starting Chessly real-time monitoring...");
      const result = await this.browserAutomation.startMonitoring(
        playerColor,
        this.handlePositionChange.bind(this),
        this.handleError.bind(this)
      );
      if (!result.success) {
        return result;
      }
      this.state.isActive = true;
      this.state.website = this.browserAutomation.getCurrentWebsite();
      this.state.playerColor = playerColor;
      this.state.analysisCount = 0;
      this.notifyRenderer("monitoring-started", {
        website: this.state.website,
        playerColor: this.state.playerColor
      });
      console.log(`✅ Monitoring started for ${this.state.website} as ${this.state.playerColor}`);
      return { success: true };
    } catch (error) {
      console.error("Error starting monitoring:", error);
      return { success: false, error: "Failed to start monitoring" };
    }
  }
  stopMonitoring() {
    if (!this.state.isActive) {
      return;
    }
    console.log("🛑 Stopping Chessly monitoring...");
    this.browserAutomation.stopMonitoring();
    this.state = {
      isActive: false,
      website: null,
      playerColor: null,
      currentFen: null,
      lastMoveTime: 0,
      analysisCount: 0
    };
    this.notifyRenderer("monitoring-stopped", {});
    console.log("✅ Monitoring stopped");
  }
  getState() {
    return { ...this.state };
  }
  isMonitoring() {
    return this.state.isActive;
  }
  async handlePositionChange(fen, website) {
    try {
      console.log(`🔄 Position changed on ${website}:`, fen);
      this.state.currentFen = fen;
      this.state.lastMoveTime = Date.now();
      this.state.analysisCount++;
      const playerColor = this.state.playerColor === "white" ? "w" : "b";
      const position = await this.chessManager.analyzePosition(
        fen,
        playerColor,
        (analysis) => {
          this.notifyRenderer("analysis-update", {
            analysis,
            position: this.state,
            bestMove: analysis.bestMove,
            evaluation: this.formatEvaluation(analysis)
          });
        },
        (position2) => {
          this.notifyRenderer("position-update", {
            position: position2,
            state: this.state
          });
        }
      );
      if (position) {
        console.log(`📊 Analysis #${this.state.analysisCount} completed`);
        const bestMove = this.chessManager.getBestMoveText();
        const evaluation = this.chessManager.getEvaluationText();
        const bestMoveHighlight = this.chessManager.getBestMoveHighlight();
        console.log(`💡 ${bestMove}`);
        console.log(`📈 ${evaluation}`);
        if (bestMoveHighlight?.move) {
          await this.browserAutomation.highlightBestMove(
            bestMoveHighlight.move.from,
            bestMoveHighlight.move.to
          );
        }
        this.notifyRenderer("move-suggestion", {
          bestMove: bestMoveHighlight,
          evaluation,
          moveText: bestMove,
          fen,
          analysisCount: this.state.analysisCount,
          website: this.state.website,
          playerColor: this.state.playerColor
        });
      }
    } catch (error) {
      console.error("Error handling position change:", error);
      this.handleError("Failed to analyze position");
    }
  }
  handleError(error) {
    console.error("🚨 Monitoring error:", error);
    this.notifyRenderer("monitoring-error", {
      error,
      timestamp: Date.now()
    });
  }
  formatEvaluation(analysis) {
    if (!analysis) return "Analyzing...";
    if (analysis.mate !== void 0) {
      const mateIn = Math.abs(analysis.mate);
      const side = analysis.mate > 0 ? "White" : "Black";
      return `${side} mates in ${mateIn}`;
    }
    const evaluation = analysis.evaluation || 0;
    if (Math.abs(evaluation) < 0.1) {
      return "Equal position";
    } else if (evaluation > 0) {
      return `White +${evaluation.toFixed(1)}`;
    } else {
      return `Black +${Math.abs(evaluation).toFixed(1)}`;
    }
  }
  notifyRenderer(event, data) {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(`realtime-${event}`, data);
    }
  }
  // Manual position input for testing
  async analyzeManualPosition(fen) {
    if (!this.state.isActive) {
      console.log("Starting manual analysis mode...");
      this.state.isActive = true;
      this.state.website = "Manual Input";
      this.state.playerColor = "white";
    }
    await this.handlePositionChange(fen, "Manual Input");
  }
  // Quick start for testing without browser integration
  async startQuickMode(playerColor = "white") {
    console.log("🚀 Starting Chessly in quick mode...");
    this.state.isActive = true;
    this.state.website = "Quick Mode";
    this.state.playerColor = playerColor;
    this.state.analysisCount = 0;
    const startingFen = "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1";
    await this.analyzeManualPosition(startingFen);
    this.notifyRenderer("monitoring-started", {
      website: this.state.website,
      playerColor: this.state.playerColor,
      mode: "quick"
    });
    console.log(`✅ Quick mode started as ${playerColor}`);
  }
  // Simulate position changes for testing
  async simulateGame() {
    if (!this.state.isActive) {
      await this.startQuickMode();
    }
    const testPositions = [
      "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1",
      "rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1",
      "rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2",
      "rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2",
      "rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3"
    ];
    console.log("🎮 Simulating chess game...");
    for (let i = 0; i < testPositions.length; i++) {
      await new Promise((resolve) => setTimeout(resolve, 3e3));
      await this.analyzeManualPosition(testPositions[i]);
    }
  }
  destroy() {
    this.stopMonitoring();
    this.chessManager.destroy();
  }
}
const state = {
  mainWindow: null,
  isWindowVisible: false,
  windowPosition: null,
  windowSize: null,
  screenWidth: 0,
  screenHeight: 0,
  step: 0,
  currentX: 0,
  currentY: 0,
  keyboardShortcutHelper: null,
  screenshotManager: null,
  chessManager: null,
  realtimeMonitor: null,
  view: "analysis",
  currentPosition: null,
  isAnalyzing: false,
  isMonitoring: false,
  CHESS_EVENTS: {
    NO_SCREENSHOTS: "chess-no-screenshots",
    BOARD_DETECTED: "chess-board-detected",
    ANALYSIS_START: "chess-analysis-start",
    ANALYSIS_UPDATE: "chess-analysis-update",
    BEST_MOVE_FOUND: "chess-best-move-found",
    ANALYSIS_ERROR: "chess-analysis-error",
    MONITORING_STARTED: "realtime-monitoring-started",
    MONITORING_STOPPED: "realtime-monitoring-stopped",
    MOVE_SUGGESTION: "realtime-move-suggestion"
  }
};
async function createWindow() {
  if (state.mainWindow) {
    if (state.mainWindow.isMinimized()) state.mainWindow.restore();
    state.mainWindow.focus();
    return;
  }
  const primaryDisplay = electron.screen.getPrimaryDisplay();
  const workArea = primaryDisplay.workAreaSize;
  state.screenWidth = workArea.width;
  state.screenHeight = workArea.height;
  state.step = 60;
  state.currentY = 50;
  const windowSettings = {
    width: 600,
    height: 400,
    minWidth: 500,
    minHeight: 350,
    x: state.currentX,
    y: state.currentY,
    alwaysOnTop: true,
    frame: false,
    transparent: true,
    fullscreenable: false,
    hasShadow: false,
    opacity: 0.9,
    backgroundColor: "#00000000",
    focusable: true,
    skipTaskbar: true,
    type: "panel",
    paintWhenInitiallyHidden: true,
    titleBarStyle: "hidden",
    enableLargerThanScreen: true,
    movable: true,
    show: false,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? { icon } : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      nodeIntegration: false,
      contextIsolation: true,
      scrollBounce: true
    }
  };
  state.mainWindow = new electron.BrowserWindow(windowSettings);
  if (state.realtimeMonitor) {
    state.realtimeMonitor.setMainWindow(state.mainWindow);
  }
  state.mainWindow.on("ready-to-show", () => {
    state.mainWindow?.show();
  });
  state.mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (utils.is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    state.mainWindow?.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    state.mainWindow?.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
  state.mainWindow.webContents.setZoomFactor(1);
  state.mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  state.mainWindow.setContentProtection(true);
  state.mainWindow.setVisibleOnAllWorkspaces(true, {
    visibleOnFullScreen: true
  });
  state.mainWindow.setAlwaysOnTop(true, "screen-saver", 1);
  if (process.platform === "darwin") {
    state.mainWindow.setHiddenInMissionControl(true);
    state.mainWindow.setWindowButtonVisibility(false);
    state.mainWindow.setBackgroundColor("#00000000");
    state.mainWindow.setSkipTaskbar(true);
    state.mainWindow.setHasShadow(false);
  }
  state.mainWindow.on("close", () => {
    state.mainWindow = null;
    state.isWindowVisible = false;
  });
  state.mainWindow.webContents.setBackgroundThrottling(false);
  state.mainWindow.webContents.setFrameRate(60);
  state.mainWindow.on("move", handleWindowMove);
  state.mainWindow.on("resize", handleWindowResize);
  state.mainWindow.on("closed", handleWindowClosed);
  const bounds = state.mainWindow.getBounds();
  state.windowPosition = { x: bounds.x, y: bounds.y };
  state.windowSize = { width: bounds.width, height: bounds.height };
  state.currentX = bounds.x;
  state.currentY = bounds.y;
  state.isWindowVisible = true;
  const savedOpacity = configManager.getOpacity();
  console.log("savedOpacity", savedOpacity);
  state.mainWindow.showInactive();
  if (savedOpacity <= 0.1) {
    console.log("Initial opacity too low, setting to 0 and hiding window");
    state.mainWindow.setOpacity(0);
    state.isWindowVisible = false;
  } else {
    console.log("Setting opacity to", savedOpacity);
    state.mainWindow.setOpacity(savedOpacity);
    state.isWindowVisible = true;
  }
}
function getMainWindow() {
  return state.mainWindow;
}
async function takeScreenshot() {
  if (!state.mainWindow) throw new Error("Main window not found");
  const screenshotPath = await (state.screenshotManager?.takeScreenshot(
    () => hideMainWindow(),
    () => showMainWindow()
  ) || "");
  if (screenshotPath && state.chessManager) {
    try {
      const position = await state.chessManager.analyzeScreenshot(
        screenshotPath,
        (analysis) => {
          state.chessManager?.sendAnalysisToRenderer(state.mainWindow);
        },
        (position2) => {
          if (state.mainWindow && !state.mainWindow.isDestroyed()) {
            state.mainWindow.webContents.send(state.CHESS_EVENTS.BOARD_DETECTED, position2);
          }
        }
      );
      if (position) {
        state.currentPosition = position;
        state.isAnalyzing = true;
        if (state.mainWindow && !state.mainWindow.isDestroyed()) {
          state.mainWindow.webContents.send(state.CHESS_EVENTS.ANALYSIS_START, position);
        }
      } else {
        if (state.mainWindow && !state.mainWindow.isDestroyed()) {
          state.mainWindow.webContents.send(state.CHESS_EVENTS.NO_SCREENSHOTS);
        }
      }
    } catch (error) {
      console.error("Error analyzing chess screenshot:", error);
      if (state.mainWindow && !state.mainWindow.isDestroyed()) {
        state.mainWindow.webContents.send(state.CHESS_EVENTS.ANALYSIS_ERROR, error);
      }
    }
  }
  return screenshotPath;
}
async function getImagePreview(filePath) {
  return state.screenshotManager?.getImagePreview(filePath) || "";
}
function setView(view) {
  state.view = view;
  state.screenshotManager?.setView("queue");
}
function getView() {
  return state.view;
}
function clearQueues() {
  state.screenshotManager?.clearQueues();
  state.currentPosition = null;
  state.isAnalyzing = false;
  state.chessManager?.clearPosition();
  setView("analysis");
}
function getScreenshotQueue() {
  return state.screenshotManager?.getScreenshotQueue() || [];
}
function getExtraScreenshotQueue() {
  return state.screenshotManager?.getExtraScreenshotQueue() || [];
}
async function deleteScreenshot(path2) {
  return state.screenshotManager?.deleteScreenshot(path2) || {
    success: false,
    error: "Failed to delete screenshot"
  };
}
function handleWindowMove() {
  if (!state.mainWindow) return;
  const bounds = state.mainWindow.getBounds();
  state.windowPosition = { x: bounds.x, y: bounds.y };
  state.currentX = bounds.x;
  state.currentY = bounds.y;
}
function handleWindowResize() {
  if (!state.mainWindow) return;
  const bounds = state.mainWindow.getBounds();
  state.windowSize = { width: bounds.width, height: bounds.height };
}
function handleWindowClosed() {
  state.mainWindow = null;
  state.isWindowVisible = false;
  state.windowPosition = null;
  state.windowSize = null;
}
function moveWindowHorizontal(updateFn) {
  if (!state.mainWindow) return;
  state.currentX = updateFn(state.currentX);
  state.mainWindow.setPosition(Math.round(state.currentX), Math.round(state.currentY));
}
function moveWindowVertical(updateFn) {
  if (!state.mainWindow) return;
  const newY = updateFn(state.currentY);
  const maxUpLimit = -(state.windowSize?.height || 0) * 2 / 3;
  const maxDownLimit = state.screenHeight + (state.windowSize?.height || 0) * 2 / 3;
  console.log({
    newY,
    maxUpLimit,
    maxDownLimit,
    screenHeight: state.screenHeight,
    windowHeight: state.windowSize?.height,
    currentY: state.currentY
  });
  if (newY >= maxUpLimit && newY <= maxDownLimit) {
    state.currentY = newY;
    state.mainWindow.setPosition(Math.round(state.currentX), Math.round(state.currentY));
  }
}
function hideMainWindow() {
  if (!state.mainWindow?.isDestroyed()) {
    const bounds = state.mainWindow?.getBounds();
    if (!bounds) return;
    state.windowPosition = { x: bounds.x, y: bounds.y };
    state.windowSize = { width: bounds.width, height: bounds.height };
    state.mainWindow?.setIgnoreMouseEvents(true, { forward: true });
    state.mainWindow?.setOpacity(0);
    state.isWindowVisible = false;
    console.log("Hiding main window");
  }
}
function showMainWindow() {
  if (!state.mainWindow?.isDestroyed()) {
    if (state.windowPosition && state.windowSize) {
      state?.mainWindow?.setBounds({
        ...state.windowPosition,
        ...state.windowSize
      });
    }
    state.mainWindow?.setIgnoreMouseEvents(false);
    state.mainWindow?.setAlwaysOnTop(true, "screen-saver", 1);
    state.mainWindow?.setVisibleOnAllWorkspaces(true, { visibleOnFullScreen: true });
    state.mainWindow?.setContentProtection(true);
    state.mainWindow?.setOpacity(0);
    state.mainWindow?.showInactive();
    state.mainWindow?.setOpacity(1);
    state.isWindowVisible = true;
    console.log("Showing main window");
  }
}
function toggleMainWindow() {
  console.log("Toggling main window");
  if (state.isWindowVisible) {
    hideMainWindow();
  } else {
    showMainWindow();
  }
}
function getCurrentPosition() {
  return state.currentPosition;
}
function setCurrentPosition(position) {
  state.currentPosition = position;
}
function getIsAnalyzing() {
  return state.isAnalyzing;
}
function setIsAnalyzing(analyzing) {
  state.isAnalyzing = analyzing;
}
function initializeHelpers() {
  state.screenshotManager = new ScreenshotManager("queue");
  state.chessManager = new ChessManager();
  state.realtimeMonitor = new RealtimeMonitor(state.chessManager, {
    pollInterval: 1e3,
    // Check every second
    autoStart: false,
    enableNotifications: true
  });
  state.keyboardShortcutHelper = new KeyboardShortcutHelper({
    moveWindowLeft: () => moveWindowHorizontal((x) => Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)),
    moveWindowRight: () => moveWindowHorizontal(
      (x) => Math.min(state.screenWidth - (state.windowSize?.width || 0) / 2, x + state.step)
    ),
    moveWindowUp: () => moveWindowVertical((y) => y - state.step),
    moveWindowDown: () => moveWindowVertical((y) => y + state.step),
    toggleMainWindow,
    isVisible: () => state.isWindowVisible,
    getMainWindow,
    takeScreenshot,
    getImagePreview,
    clearQueues,
    setView,
    chessManager: state.chessManager
  });
}
function setWindowDimensions(width, height) {
  if (!state.mainWindow?.isDestroyed()) {
    const [currentX, currentY] = state.mainWindow?.getPosition() || [0, 0];
    const primaryDisplay = electron.screen.getPrimaryDisplay();
    const workArea = primaryDisplay.workAreaSize;
    const maxWidth = Math.floor(workArea.width * 0.5);
    state.mainWindow?.setBounds({
      x: Math.min(currentX, workArea.width - maxWidth),
      y: currentY,
      width: Math.min(width + 32, maxWidth),
      height: Math.ceil(height)
    });
  }
}
async function initializeApp() {
  try {
    const appDataPath = path.join(electron.app.getPath("appData"), "chessly");
    const sessionPath = path.join(appDataPath, "session");
    const tempPath = path.join(appDataPath, "temp");
    const cachePath = path.join(appDataPath, "cache");
    console.log("Chessly app data path:", appDataPath);
    for (const dir of [appDataPath, sessionPath, tempPath, cachePath]) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
      }
    }
    electron.app.setPath("userData", appDataPath);
    electron.app.setPath("sessionData", sessionPath);
    electron.app.setPath("temp", tempPath);
    electron.app.setPath("cache", cachePath);
    initializeHelpers();
    initializeIpcHandler({
      getView,
      getMainWindow,
      takeScreenshot,
      clearQueues,
      setView,
      moveWindowLeft: () => moveWindowHorizontal((x) => Math.max(-(state.windowSize?.width || 0) / 2, x - state.step)),
      moveWindowRight: () => moveWindowHorizontal(
        (x) => Math.min(state.screenWidth - (state.windowSize?.width || 0) / 2, x + state.step)
      ),
      moveWindowUp: () => moveWindowVertical((y) => y - state.step),
      moveWindowDown: () => moveWindowVertical((y) => y + state.step),
      toggleMainWindow,
      isVisible: () => state.isWindowVisible,
      getScreenshotQueue,
      getExtraScreenshotQueue,
      deleteScreenshot,
      getImagePreview,
      CHESS_EVENTS: state.CHESS_EVENTS,
      chessManager: state.chessManager,
      realtimeMonitor: state.realtimeMonitor,
      setWindowDimensions,
      getCurrentPosition,
      setCurrentPosition,
      getIsAnalyzing,
      setIsAnalyzing
    });
    await createWindow();
    state.keyboardShortcutHelper?.registerGlobalShortcuts();
  } catch (error) {
    console.error("Failed to initialize app:", error);
    electron.app.quit();
  }
}
electron.app.whenReady().then(initializeApp);
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
exports.state = state;
