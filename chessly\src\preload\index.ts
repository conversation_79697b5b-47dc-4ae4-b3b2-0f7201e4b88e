import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'

export const CHESS_EVENTS = {
  NO_SCREENSHOTS: 'chess-no-screenshots',
  BOARD_DETECTED: 'chess-board-detected',
  ANALYSIS_START: 'chess-analysis-start',
  ANALYSIS_UPDATE: 'chess-analysis-update',
  BEST_MOVE_FOUND: 'chess-best-move-found',
  ANALYSIS_ERROR: 'chess-analysis-error',
  MOVE_HIGHLIGHT: 'chess-move-highlight',
  RESET: 'chess-reset-view',
  FORCE_ANALYSIS: 'chess-force-analysis'
}

const electronAPI = {
  getConfig: () => ipcRenderer.invoke('get-config'),
  updateConfig: (config: {
    opacity?: number
    autoAnalyze?: boolean
    showSquareNames?: boolean
    highlightColor?: string
  }) => ipcRenderer.invoke('update-config', config),
  getScreenshots: () => ipcRenderer.invoke('get-screenshots'),
  deleteScreenshot: (path: string) => ipcRenderer.invoke('delete-screenshot', path),
  toggleMainWindow: async () => {
    console.log('toggleMainWindow called from preload')
    try {
      const result = await ipcRenderer.invoke('toggle-main-window')
      return result
    } catch (error) {
      console.error('Error toggling main window:', error)
      throw error
    }
  },

  // Chess-specific API methods
  getCurrentPosition: () => ipcRenderer.invoke('get-current-position'),
  getIsAnalyzing: () => ipcRenderer.invoke('get-is-analyzing'),
  stopChessAnalysis: () => ipcRenderer.invoke('stop-chess-analysis'),
  getBestMove: () => ipcRenderer.invoke('get-best-move'),
  getEvaluation: () => ipcRenderer.invoke('get-evaluation'),
  getSquareInfo: (file: string, rank: number) => ipcRenderer.invoke('get-square-info', file, rank),
  getAllSquares: () => ipcRenderer.invoke('get-all-squares'),
  triggerChessAnalysis: () => ipcRenderer.invoke('trigger-chess-analysis'),

  // Real-time monitoring API methods
  startRealtimeMonitoring: (playerColor: 'white' | 'black') => ipcRenderer.invoke('start-realtime-monitoring', playerColor),
  stopRealtimeMonitoring: () => ipcRenderer.invoke('stop-realtime-monitoring'),
  getMonitoringState: () => ipcRenderer.invoke('get-monitoring-state'),
  startQuickMode: (playerColor: 'white' | 'black') => ipcRenderer.invoke('start-quick-mode', playerColor),
  simulateGame: () => ipcRenderer.invoke('simulate-game'),
  analyzeManualPosition: (fen: string) => ipcRenderer.invoke('analyze-manual-position', fen),
  onScreenshotTaken: (callback: (data: { path: string; preview: string }) => void) => {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const subscription = (_: any, data: { path: string; preview: string }) => callback(data)
    ipcRenderer.on('screenshot-taken', subscription)
    return () => ipcRenderer.removeListener('screenshot-taken', subscription)
  },
  getPlatform: () => process.platform,
  triggerScreenshot: () => ipcRenderer.invoke('trigger-screenshot'),
  deleteLastScreenshot: () => ipcRenderer.invoke('delete-last-screenshot'),

  // Chess event handlers
  onChessBoardDetected: (callback: (position: any) => void) => {
    const subscription = (_: any, position: any) => callback(position)
    ipcRenderer.on(CHESS_EVENTS.BOARD_DETECTED, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.BOARD_DETECTED, subscription)
  },
  onChessAnalysisStart: (callback: (position: any) => void) => {
    const subscription = (_: any, position: any) => callback(position)
    ipcRenderer.on(CHESS_EVENTS.ANALYSIS_START, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_START, subscription)
  },
  onChessAnalysisUpdate: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(CHESS_EVENTS.ANALYSIS_UPDATE, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_UPDATE, subscription)
  },
  onChessMoveHighlight: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on(CHESS_EVENTS.MOVE_HIGHLIGHT, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.MOVE_HIGHLIGHT, subscription)
  },
  onChessAnalysisError: (callback: (error: any) => void) => {
    const subscription = (_: any, error: any) => callback(error)
    ipcRenderer.on(CHESS_EVENTS.ANALYSIS_ERROR, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_ERROR, subscription)
  },
  onChessNoScreenshots: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(CHESS_EVENTS.NO_SCREENSHOTS, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.NO_SCREENSHOTS, subscription)
  },
  onChessReset: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on(CHESS_EVENTS.RESET, subscription)
    return () => ipcRenderer.removeListener(CHESS_EVENTS.RESET, subscription)
  },

  // Real-time monitoring event handlers
  onRealtimeMonitoringStarted: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on('realtime-monitoring-started', subscription)
    return () => ipcRenderer.removeListener('realtime-monitoring-started', subscription)
  },
  onRealtimeMonitoringStopped: (callback: () => void) => {
    const subscription = () => callback()
    ipcRenderer.on('realtime-monitoring-stopped', subscription)
    return () => ipcRenderer.removeListener('realtime-monitoring-stopped', subscription)
  },
  onRealtimeMoveSuggestion: (callback: (data: any) => void) => {
    const subscription = (_: any, data: any) => callback(data)
    ipcRenderer.on('realtime-move-suggestion', subscription)
    return () => ipcRenderer.removeListener('realtime-move-suggestion', subscription)
  },
  triggerReset: () => ipcRenderer.invoke('trigger-reset'),
  updateContentDimensions: (dimensions: { width: number; height: number }) =>
    ipcRenderer.invoke('update-content-dimensions', dimensions),
  openLink: (url: string) => ipcRenderer.invoke('openLink', url),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  removeListener: (eventName: string, callback: (...args: any[]) => void) => {
    ipcRenderer.removeListener(eventName, callback)
  },
  removeAllListeners: () => {
    // Remove all chess event listeners
    Object.values(CHESS_EVENTS).forEach(event => {
      ipcRenderer.removeAllListeners(event)
    })
    ipcRenderer.removeAllListeners('screenshot-taken')
    ipcRenderer.removeAllListeners('screenshot-deleted')
  }
}

contextBridge.exposeInMainWorld('electronAPI', electronAPI)
