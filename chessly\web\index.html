<!doctype html><html lang="en"><head><meta charset="UTF-8" /><meta name="viewport" content="width=device-width, initial-scale=1.0" /><title>Cluelessly</title><meta name="description" content="Cluelessly - The undetectable AI that sees your screen, codes for you, and feeds you answers — in real time ;)" /><meta name="keywords" content="Cluelessly, AI, undetectable, screen, code, answers, real time" /><meta property="og:image" content="https://i.postimg.cc/cdqKxKyy/700-1x-shots-so.jpg" /><meta name="author" content="Xeven" /><meta name="robots" content="index, follow" /><link rel="preconnect" href="https://fonts.googleapis.com" /><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin /><link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Host+Grotesk:ital,wght@0,300..800;1,300..800&display=swap" /><link href="https://fonts.googleapis.com/css2?family=Host+Grotesk:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet" media="print" onload="this.media='all'" /><noscript><link href="https://fonts.googleapis.com/css2?family=Host+Grotesk:ital,wght@0,300..800;1,300..800&display=swap" rel="stylesheet" /></noscript><script src="https://cdn.tailwindcss.com"></script><style>*{ margin: 0; padding: 0; box-sizing: border-box;} body{ font-family: 'Host Grotesk', sans-serif; scroll-behavior: smooth; overflow-x: hidden;} ::selection{ background-color: rgba(59, 176, 255); color: white;} ::-webkit-scrollbar{ display: none;} .glow-overlay1{ box-shadow: inset 0 -25px 50px rgba(59, 130, 246, 0.6), 0 0 30px rgba(59, 130, 246, 0.2); backdrop-filter: blur(8px);} .glow-overlay2{ box-shadow: inset 0 -20px 60px rgba(246, 128, 59, 0.6), 0 0 30px rgba(246, 103, 59, 0.2); backdrop-filter: blur(8px);} .glow-overlay3{ box-shadow: inset 0 -20px 60px rgba(107, 114, 114, 0.6), 0 0 30px rgba(107, 103, 101, 0.2); backdrop-filter: blur(8px);} @keyframes float{ 0%, 100%{ transform: translateY(0px);} 50%{ transform: translateY(-10px);}} @keyframes glow{ 0%, 100%{ box-shadow: 0 0 20px rgba(50, 129, 255, 0.3);} 50%{ box-shadow: 0 0 50px rgba(59, 176, 255, 0.6);}} @keyframes rotate{ from{ transform: rotate(0deg);} to{ transform: rotate(360deg);}} .vibrate-1:hover{ -webkit-animation: vibrate-1 0.3s linear infinite both; animation: vibrate-1 0.3s linear infinite both;} @-webkit-keyframes vibrate-1{ 0%{ -webkit-transform: translate(0); transform: translate(0);} 20%{ -webkit-transform: translate(-2px, 2px); transform: translate(-2px, 2px);} 40%{ -webkit-transform: translate(-2px, -2px); transform: translate(-2px, -2px);} 60%{ -webkit-transform: translate(2px, 2px); transform: translate(2px, 2px);} 80%{ -webkit-transform: translate(2px, -2px); transform: translate(2px, -2px);} 100%{ -webkit-transform: translate(0); transform: translate(0);}} @keyframes vibrate-1{ 0%{ -webkit-transform: translate(0); transform: translate(0);} 20%{ -webkit-transform: translate(-2px, 2px); transform: translate(-2px, 2px);} 40%{ -webkit-transform: translate(-2px, -2px); transform: translate(-2px, -2px);} 60%{ -webkit-transform: translate(2px, 2px); transform: translate(2px, 2px);} 80%{ -webkit-transform: translate(2px, -2px); transform: translate(2px, -2px);} 100%{ -webkit-transform: translate(0); transform: translate(0);}} @-webkit-keyframes text-focus-in{ 0%{ -webkit-filter: blur(12px); filter: blur(12px); opacity: 0;} 100%{ -webkit-filter: blur(0px); filter: blur(0px); opacity: 1;}} @keyframes text-focus-in{ 0%{ -webkit-filter: blur(12px); filter: blur(12px); opacity: 0;} 100%{ -webkit-filter: blur(0px); filter: blur(0px); opacity: 1;}} .float{ animation: float 3s ease-in-out infinite;} .glow{ animation: glow 2s ease-in-out infinite;} .rotate{ animation: rotate 20s linear infinite;} .text-focus-in{ -webkit-animation: text-focus-in 1s cubic-bezier(0.55, 0.085, 0.68, 0.53) both; animation: text-focus-in 1s cubic-bezier(0.55, 0.085, 0.68, 0.53) both;} .scroll-watcher{ animation: scroll-watcher linear forwards; animation-timeline: view(390px 0px);} @keyframes scroll-watcher{ from{ opacity: 0; filter: blur(3px);} to{ opacity: 1; filter: blur(0px);}} </style></head><body class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-cyan-50"><nav class="flex items-center justify-between px-8 py-2 backdrop-blur-lg bg-white/70 shadow shadow-zinc-200 rounded-full md:w-[90%] w-full mx-auto fixed top-2 z-50 -translate-x-1/2 left-1/2"><div class="flex items-center space-x-2 text-2xl font-bold text-zinc-900"><div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full hover:animate-spin"></div><span>Cluelessly</span></div><div class="hidden md:flex items-center space-x-8 text-zinc-700"><a href="#how-it-works" class="hover:text-blue-600 transition-colors">How it works</a><a href="#use-cases" class="hover:text-blue-600 transition-colors">Use cases</a></div><button class="bg-zinc-800 text-white px-6 py-2 rounded-full hover:bg-zinc-700 transition-colors flex items-center space-x-2"><span>Follow</span><svg class="w-4 h-4 hidden sm:block" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path></svg></button></nav><section class="px-8 py-20 min-h-dvh flex flex-col items-center justify-center text-center relative overflow-hidden"><img src="./large-comet.svg" class="absolute -bottom-20 -right-60 opacity-20 animate-pulse brightness-150" alt="" /><img src="./small-comet.svg" class="absolute -top-2 -left-20 opacity-40 animate-pulse brightness-150" alt="" /><div class="absolute inset-0 overflow-hidden"><div class="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-r from-blue-400/20 to-cyan-400/20 rounded-full blur-3xl rotate"></div><div class="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-r from-cyan-400/20 to-pink-400/20 rounded-full blur-3xl rotate" style="animation-direction: reverse; animation-duration: 15s"></div></div><div class="max-w-4xl mx-auto relative z-10"><h1 class="text-5xl leading-[0.96] md:text-7xl font-bold mb-6 md:leading-tight tracking-tight text-focus-in"><span class="bg-gradient-to-b from-zinc-700/80 to-zinc-900 bg-clip-text text-transparent">Everything You Need, </span><br /><span class="bg-gradient-to-r from-blue-600 to-cyan-500 bg-clip-text text-transparent text-focus-in">is Before Your Eyes. </span></h1><p class="text-xl text-zinc-500 mb-12 max-w-2xl mx-auto md:leading-normal leading-snug">Cluelessly is an undetectable AI that sees your screen, <br />codes for you, and feeds you answers — in real time ;) </p><a href="https://github.com/Xeven777/clueless-coder/releases" target="_blank"><button class="bg-zinc-800 text-white px-8 py-4 rounded-xl flex items-center space-x-3 text-lg font-medium shadow-2xl hover:bg-zinc-700 duration-300 active:scale-95 glow transition-all mx-auto"><span>Download Now</span></button></a></div></section><section id="use-cases" class="px-8 min-h-dvh py-20 flex items-center justify-center bg-gradient-to-r from-slate-900 via-black to-slate-900 text-white"><div class="max-w-6xl mx-auto text-center"><h2 class="text-4xl md:text-5xl font-semibold tracking-tight mb-6">the Best AI models for <span class="bg-gradient-to-r italic font-light text-5xl md:text-6xl tracking-tighter from-blue-100 to-cyan-50 bg-clip-text text-transparent">Free </span></h2><p class="text-xl text-zinc-400 mb-16">Cutting edge AI models <br />which are best for coding and reasoning </p><div class="grid items-center grid-cols-1 md:grid-cols-3 max-w-6xl gap-6 md:gap-12"><div class="relative cursor-pointer mx-auto hover:scale-105 transition-all duration-300 flex items-center justify-center w-64 h-64 rounded-3xl bg-gradient-to-b from-black to-slate-950 overflow-hidden"><div class="absolute bottom-0 left-0 w-full h-full flex flex-col items-center justify-end pointer-events-none z-0"><div class="glow-layer w-11/12 h-3 rounded-full bg-blue-900 opacity-60 blur-lg mb-1"></div><div class="glow-layer w-10/12 h-3 rounded-full bg-blue-700 opacity-50 blur-md mb-1"></div><div class="glow-layer w-9/12 h-3 rounded-full bg-blue-100 opacity-70 blur-md"></div></div><div class="relative z-20 text-4xl md:text-5xl font-bold text-center m-auto"><img src="./gemini.svg" alt="Gemini" class="mb-6 size-24 mx-auto" /><span class="bg-gradient-to-b from-blue-200 to-cyan-50 bg-clip-text text-transparent">GEMINI</span></div><div class="absolute inset-0 rounded-3xl pointer-events-none z-10 glow-overlay1"></div></div><div class="relative cursor-pointer mx-auto hover:scale-105 transition-all duration-300 flex items-center justify-center w-64 h-64 rounded-3xl bg-gradient-to-b from-black to-slate-950 overflow-hidden"><div class="absolute bottom-0 left-0 w-full h-full flex flex-col items-center justify-end pointer-events-none z-0"><div class="glow-layer w-11/12 h-3 rounded-full bg-red-900 opacity-60 blur-lg mb-1"></div><div class="glow-layer w-10/12 h-3 rounded-full bg-orange-700 opacity-50 blur-md mb-1"></div><div class="glow-layer w-9/12 h-3 rounded-full bg-orange-100 opacity-70 blur-md"></div></div><div class="relative z-20 text-4xl md:text-5xl font-bold text-center m-auto"><img src="./groq.svg" alt="Gemini" class="mb-6 size-24 mx-auto" /><span class="bg-gradient-to-b from-orange-200 to-amber-50 bg-clip-text text-transparent">GROQ</span></div><div class="absolute inset-0 rounded-3xl pointer-events-none z-10 glow-overlay2"></div></div><div class="relative cursor-pointer mx-auto hover:scale-105 transition-all duration-300 flex items-center justify-center w-64 h-64 rounded-3xl bg-gradient-to-b from-black to-slate-950 overflow-hidden"><div class="absolute bottom-0 left-0 w-full h-full flex flex-col items-center justify-end pointer-events-none z-0"><div class="glow-layer w-11/12 h-3 rounded-full bg-zinc-900 opacity-60 blur-lg mb-1"></div><div class="glow-layer w-10/12 h-3 rounded-full bg-zinc-700 opacity-50 blur-md mb-1"></div><div class="glow-layer w-9/12 h-3 rounded-full bg-zinc-100 opacity-70 blur-md"></div></div><div class="relative z-20 text-4xl md:text-5xl font-bold text-center m-auto"><img src="./openai.svg" alt="Gemini" class="mb-6 size-24 mx-auto invert" /><span class="bg-gradient-to-b from-zinc-400 to-zinc-50 bg-clip-text text-transparent">OPENAI</span></div><div class="absolute inset-0 rounded-3xl pointer-events-none z-10 glow-overlay3"></div></div></div></div></section><section class="px-8 min-h-[80vh] pt-10 md:py-20 flex flex-col items-center justify-center"><h2 class="text-5xl scroll-watcher md:text-6xl text-center font-bold mb-10">“But, this feels like cheating!” </h2><button type="button" class="bg-zinc-800 text-lg font-semibold md:text-xl text-white px-8 py-3 rounded-full hover:bg-zinc-700 flex items-center space-x-2 active:scale-95 duration-300 transition-all glow">We know ;) </button></section><section id="use-cases" class="px-8 min-h-dvh py-20 flex items-center justify-center bg-gradient-to-r from-slate-900 via-black to-slate-900 text-white"><div class="max-w-6xl mx-auto text-center"><h2 class="text-4xl md:text-5xl font-semibold tracking-tight mb-6">Undetectable by <span class="bg-gradient-to-r italic font-light text-5xl md:text-6xl tracking-tighter from-blue-100 to-cyan-50 bg-clip-text text-transparent">Design </span></h2><p class="text-xl text-zinc-400 mb-16">No bots in the room. No Zoom guests. No screen-share <br />trails. Works on everything. </p><div class="grid grid-cols-2 md:grid-cols-4 gap-6"><div class="p-6 rounded-2xl bg-gradient-to-r from-green-400 to-blue-500 transform hover:scale-105 transition-transform"><div class="w-12 h-12 mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z" /></svg></div><div class="font-medium text-white">Slack</div></div><div class="p-6 rounded-2xl bg-gradient-to-r from-blue-400 to-green-500 transform hover:scale-105 transition-transform"><div class="w-12 h-12 mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0C5.372 0 0 5.373 0 12s5.372 12 12 12 12-5.373 12-12S18.628 0 12 0zm5.568 8.16l-5.294 5.294a1.2 1.2 0 01-1.697 0L7.432 10.32a1.2 1.2 0 010-1.697 1.2 1.2 0 011.697 0l2.296 2.297L16.265 6.48a1.2 1.2 0 011.697 0 1.2 1.2 0 01-.394 1.68z" /></svg></div><div class="font-medium text-white">Google Meet</div></div><div class="p-6 rounded-2xl bg-gradient-to-r from-blue-500 to-cyan-500 transform hover:scale-105 transition-transform"><div class="w-12 h-12 mx-auto mb-4 flex items-center justify-center bg-white rounded-full"><span class="text-2xl font-bold text-blue-600">Z</span></div><div class="font-medium text-white">Zoom</div></div><div class="p-6 rounded-2xl bg-gradient-to-r from-blue-600 to-cyan-600 transform hover:scale-105 transition-transform"><div class="w-12 h-12 mx-auto mb-4 flex items-center justify-center"><svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0s12 5.373 12 12zm-6-5.5a3.5 3.5 0 10-7 0 3.5 3.5 0 007 0zm-7 7a3.5 3.5 0 100 7 3.5 3.5 0 000-7z" /></svg></div><div class="font-medium text-white">Teams</div></div></div></div></section><section class="px-8 py-40 pb-60" id="how-it-works"><div class="max-w-4xl mx-auto"><div class="bg-white rounded-3xl shadow-2xl overflow-hidden border border-zinc-200 float"><div class="bg-zinc-50 px-6 py-4 border-b border-zinc-200 flex items-center justify-between"><div class="flex items-center space-x-4"><div class="flex items-center space-x-2"><div class="w-3 h-3 bg-red-500 rounded-full"></div><div class="w-3 h-3 bg-yellow-500 rounded-full"></div><div class="w-3 h-3 bg-green-500 rounded-full"></div></div><div class="text-sm text-zinc-600">00:00</div></div><div class="flex items-center space-x-4"><button class="text-sm text-zinc-600 hover:text-blue-600 transition-colors">Ask AI ⌘ </button><button class="text-sm text-zinc-600 hover:text-blue-600 transition-colors">Show/Hide ⌘ \ </button><svg class="w-4 h-4 text-zinc-600" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg></div></div><div class="p-12"><div class="flex items-start space-x-4"><div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-cyan-600 rounded-full flex items-center justify-center text-white text-sm font-medium">AI </div><div class="flex-1"><div class="text-sm text-zinc-500 mb-2">AI Response</div><div class="space-y-4"><p class="text-zinc-700">I can see you're currently viewing the Cluelessly website homepage. The AI assistant that monitors your screen and audio to provide contextual help before you even ask for it. </p><div><h3 class="font-semibold text-zinc-900 mb-2">What is Cluelessly?</h3><p class="text-zinc-700">Cluelessly is a proactive AI assistant. Unlike traditional AI chatbots where you need to actively ask questions, Cluelessly can hide in the background and provide coding answers by scanning the screen. </p></div></div></div></div></div></div></div></section><footer class="bg-gradient-to-b from-zinc-800 to-zinc-950 rounded-t-3xl overflow-hidden text-white px-8 py-12"><div class="max-w-6xl mx-auto"><div class="flex items-center flex-wrap gap-4 justify-between"><div class="flex items-center space-x-2 text-2xl font-bold"><div class="w-8 h-8 bg-gradient-to-r from-blue-400 to-cyan-500 rounded-full"></div><span>Cluelessly</span></div><div class="text-zinc-400">© 2025 Cluelessly. Follow <a href="https://github.com/xeven777" target="_blank" class="text-white">Xeven</a></div></div></div></footer><script src="https://unpkg.com/lenis@1.3.4/dist/lenis.min.js"></script><script>const lenis=new Lenis({ autoRaf: true}) </script></body></html>