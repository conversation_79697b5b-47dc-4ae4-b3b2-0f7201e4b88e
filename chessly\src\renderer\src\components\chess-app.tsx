import { useCallback, useEffect, useState } from 'react'
import { Camera, Target, RotateCcw, Settings, Eye, EyeOff } from 'lucide-react'
import { cn } from '../lib/utils'

interface ChessPosition {
  fen: string
  boardDetection: any
  analysis?: any
  timestamp: number
}

interface MoveHighlight {
  from: { x: number; y: number; width: number; height: number }
  to: { x: number; y: number; width: number; height: number }
  move: {
    from: string
    to: string
    san: string
    uci: string
  }
}

export function ChessApp() {
  const [currentPosition, setCurrentPosition] = useState<ChessPosition | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [bestMove, setBestMove] = useState<string>('')
  const [evaluation, setEvaluation] = useState<string>('No position')
  const [moveHighlight, setMoveHighlight] = useState<MoveHighlight | null>(null)
  const [screenshots, setScreenshots] = useState<Array<{ path: string; preview: string }>>([])
  const [isVisible, setIsVisible] = useState(true)

  // Load screenshots
  const loadScreenshots = useCallback(async () => {
    try {
      const screenshotData = await window.electronAPI.getScreenshots()
      setScreenshots(screenshotData)
    } catch (error) {
      console.error('Error loading screenshots:', error)
    }
  }, [])

  // Take screenshot
  const takeScreenshot = useCallback(async () => {
    try {
      await window.electronAPI.triggerScreenshot()
      await loadScreenshots()
    } catch (error) {
      console.error('Error taking screenshot:', error)
    }
  }, [loadScreenshots])

  // Reset analysis
  const resetAnalysis = useCallback(async () => {
    try {
      await window.electronAPI.triggerReset()
      setCurrentPosition(null)
      setIsAnalyzing(false)
      setBestMove('')
      setEvaluation('No position')
      setMoveHighlight(null)
      await loadScreenshots()
    } catch (error) {
      console.error('Error resetting analysis:', error)
    }
  }, [loadScreenshots])

  // Toggle window visibility
  const toggleVisibility = useCallback(async () => {
    try {
      await window.electronAPI.toggleMainWindow()
      setIsVisible(!isVisible)
    } catch (error) {
      console.error('Error toggling visibility:', error)
    }
  }, [isVisible])

  // Set up event listeners
  useEffect(() => {
    const handleBoardDetected = (position: ChessPosition) => {
      console.log('Chess board detected:', position)
      setCurrentPosition(position)
      setIsAnalyzing(true)
    }

    const handleAnalysisStart = (position: ChessPosition) => {
      console.log('Chess analysis started:', position)
      setIsAnalyzing(true)
    }

    const handleAnalysisUpdate = (data: any) => {
      console.log('Chess analysis update:', data)
      if (data.position) {
        setCurrentPosition(data.position)
      }
      if (data.evaluation) {
        setEvaluation(data.evaluation)
      }
      if (data.bestMove) {
        setBestMove(data.bestMove)
      }
      if (data.highlight) {
        setMoveHighlight(data.highlight)
      }
    }

    const handleMoveHighlight = (data: any) => {
      console.log('Chess move highlight:', data)
      if (data.highlight) {
        setMoveHighlight(data.highlight)
      }
      if (data.evaluation) {
        setEvaluation(data.evaluation)
      }
      if (data.bestMove) {
        setBestMove(data.bestMove)
      }
    }

    const handleAnalysisError = (error: any) => {
      console.error('Chess analysis error:', error)
      setIsAnalyzing(false)
    }

    const handleNoScreenshots = () => {
      console.log('No chess board detected in screenshot')
      setIsAnalyzing(false)
    }

    const handleScreenshotTaken = () => {
      loadScreenshots()
    }

    const handleReset = () => {
      setCurrentPosition(null)
      setIsAnalyzing(false)
      setBestMove('')
      setEvaluation('No position')
      setMoveHighlight(null)
      loadScreenshots()
    }

    // Register event listeners
    window.electronAPI.onChessBoardDetected(handleBoardDetected)
    window.electronAPI.onChessAnalysisStart(handleAnalysisStart)
    window.electronAPI.onChessAnalysisUpdate(handleAnalysisUpdate)
    window.electronAPI.onChessMoveHighlight(handleMoveHighlight)
    window.electronAPI.onChessAnalysisError(handleAnalysisError)
    window.electronAPI.onChessNoScreenshots(handleNoScreenshots)
    window.electronAPI.onScreenshotTaken(handleScreenshotTaken)
    window.electronAPI.onChessReset(handleReset)

    // Load initial screenshots
    loadScreenshots()

    return () => {
      // Cleanup listeners
      window.electronAPI.removeAllListeners?.()
    }
  }, [loadScreenshots])

  return (
    <div className="min-h-screen bg-black/90 backdrop-blur-sm text-white p-4">
      <div className="max-w-md mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-6 h-6 text-blue-400" />
            <h1 className="text-xl font-bold">Chessly</h1>
          </div>
          <button
            onClick={toggleVisibility}
            className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
            title="Toggle Visibility"
          >
            {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
          </button>
        </div>

        {/* Status */}
        <div className="bg-white/5 rounded-lg p-4 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Status:</span>
            <span className={cn(
              "text-sm font-medium",
              isAnalyzing ? "text-yellow-400" : currentPosition ? "text-green-400" : "text-white/60"
            )}>
              {isAnalyzing ? "Analyzing..." : currentPosition ? "Ready" : "Waiting for screenshot"}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Evaluation:</span>
            <span className="text-sm font-medium text-blue-400">{evaluation}</span>
          </div>
          
          {bestMove && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-white/60">Best Move:</span>
              <span className="text-sm font-medium text-green-400">{bestMove}</span>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="grid grid-cols-3 gap-2">
          <button
            onClick={takeScreenshot}
            className="flex flex-col items-center gap-1 p-3 rounded-lg bg-blue-600 hover:bg-blue-700 transition-colors"
          >
            <Camera className="w-5 h-5" />
            <span className="text-xs">Capture</span>
          </button>
          
          <button
            onClick={resetAnalysis}
            className="flex flex-col items-center gap-1 p-3 rounded-lg bg-red-600 hover:bg-red-700 transition-colors"
          >
            <RotateCcw className="w-5 h-5" />
            <span className="text-xs">Reset</span>
          </button>
          
          <button
            className="flex flex-col items-center gap-1 p-3 rounded-lg bg-gray-600 hover:bg-gray-700 transition-colors"
          >
            <Settings className="w-5 h-5" />
            <span className="text-xs">Settings</span>
          </button>
        </div>

        {/* Screenshots */}
        {screenshots.length > 0 && (
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-2">Recent Screenshots</h3>
            <div className="grid grid-cols-2 gap-2">
              {screenshots.slice(-4).map((screenshot, index) => (
                <div key={screenshot.path} className="relative">
                  <img
                    src={screenshot.preview}
                    alt={`Screenshot ${index + 1}`}
                    className="w-full h-20 object-cover rounded border border-white/10"
                  />
                  {index === screenshots.length - 1 && currentPosition && (
                    <div className="absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Position Info */}
        {currentPosition && (
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-2">Position Details</h3>
            <div className="space-y-1 text-xs text-white/60">
              <div>FEN: {currentPosition.fen.substring(0, 30)}...</div>
              <div>Confidence: {(currentPosition.boardDetection?.confidence * 100 || 0).toFixed(1)}%</div>
              <div>Player: {currentPosition.boardDetection?.playerColor || 'Unknown'}</div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white/5 rounded-lg p-4">
          <h3 className="text-sm font-medium text-white/80 mb-2">Quick Guide</h3>
          <div className="space-y-1 text-xs text-white/60">
            <div>• Ctrl+H: Take screenshot</div>
            <div>• Ctrl+B: Toggle visibility</div>
            <div>• Ctrl+R: Reset analysis</div>
            <div>• Ctrl+Q: Quit application</div>
          </div>
        </div>
      </div>
    </div>
  )
}
