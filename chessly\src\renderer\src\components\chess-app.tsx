import { useCallback, useEffect, useState } from 'react'
import { Camera, Target, RotateCcw, Settings, Eye, EyeOff, Play, Square, Zap, HelpCircle } from 'lucide-react'
import { cn } from '../lib/utils'
import { InstructionsDialog } from './instructions-dialog'

interface ChessPosition {
  fen: string
  boardDetection: any
  analysis?: any
  timestamp: number
}

interface MoveHighlight {
  from: { x: number; y: number; width: number; height: number }
  to: { x: number; y: number; width: number; height: number }
  move: {
    from: string
    to: string
    san: string
    uci: string
  }
}

export function ChessApp() {
  const [currentPosition, setCurrentPosition] = useState<ChessPosition | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [bestMove, setBestMove] = useState<string>('')
  const [evaluation, setEvaluation] = useState<string>('No position')
  const [moveHighlight, setMoveHighlight] = useState<MoveHighlight | null>(null)
  const [screenshots, setScreenshots] = useState<Array<{ path: string; preview: string }>>([])
  const [isVisible, setIsVisible] = useState(true)
  const [isMonitoring, setIsMonitoring] = useState(false)
  const [monitoringState, setMonitoringState] = useState<any>(null)
  const [showInstructions, setShowInstructions] = useState(false)

  // Load screenshots
  const loadScreenshots = useCallback(async () => {
    try {
      const screenshotData = await window.electronAPI.getScreenshots()
      setScreenshots(screenshotData)
    } catch (error) {
      console.error('Error loading screenshots:', error)
    }
  }, [])

  // Take screenshot
  const takeScreenshot = useCallback(async () => {
    try {
      await window.electronAPI.triggerScreenshot()
      await loadScreenshots()
    } catch (error) {
      console.error('Error taking screenshot:', error)
    }
  }, [loadScreenshots])

  // Reset analysis
  const resetAnalysis = useCallback(async () => {
    try {
      await window.electronAPI.triggerReset()
      setCurrentPosition(null)
      setIsAnalyzing(false)
      setBestMove('')
      setEvaluation('No position')
      setMoveHighlight(null)
      await loadScreenshots()
    } catch (error) {
      console.error('Error resetting analysis:', error)
    }
  }, [loadScreenshots])

  // Toggle window visibility
  const toggleVisibility = useCallback(async () => {
    try {
      await window.electronAPI.toggleMainWindow()
      setIsVisible(!isVisible)
    } catch (error) {
      console.error('Error toggling visibility:', error)
    }
  }, [isVisible])

  // Start real-time monitoring
  const startMonitoring = useCallback(async (playerColor: 'white' | 'black') => {
    try {
      const result = await window.electronAPI.startRealtimeMonitoring(playerColor)
      if (result.success) {
        setIsMonitoring(true)
        console.log(`Real-time monitoring started as ${playerColor}`)
      } else {
        console.error('Failed to start monitoring:', result.error)
        alert(`Failed to start monitoring: ${result.error}`)
      }
    } catch (error) {
      console.error('Error starting monitoring:', error)
      alert('Error starting monitoring. Please make sure you have a chess game open in your browser.')
    }
  }, [])

  // Stop real-time monitoring
  const stopMonitoring = useCallback(async () => {
    try {
      await window.electronAPI.stopRealtimeMonitoring()
      setIsMonitoring(false)
      console.log('Real-time monitoring stopped')
    } catch (error) {
      console.error('Error stopping monitoring:', error)
    }
  }, [])

  // Start quick mode
  const startQuickMode = useCallback(async (playerColor: 'white' | 'black') => {
    try {
      const result = await window.electronAPI.startQuickMode(playerColor)
      if (result.success) {
        setIsMonitoring(true)
        console.log(`Quick mode started as ${playerColor}`)
      } else {
        console.error('Failed to start quick mode:', result.error)
      }
    } catch (error) {
      console.error('Error starting quick mode:', error)
    }
  }, [])

  // Simulate game
  const simulateGame = useCallback(async () => {
    try {
      const result = await window.electronAPI.simulateGame()
      if (result.success) {
        console.log('Game simulation started')
      } else {
        console.error('Failed to simulate game:', result.error)
      }
    } catch (error) {
      console.error('Error simulating game:', error)
    }
  }, [])

  // Set up event listeners
  useEffect(() => {
    const handleBoardDetected = (position: ChessPosition) => {
      console.log('Chess board detected:', position)
      setCurrentPosition(position)
      setIsAnalyzing(true)
    }

    const handleAnalysisStart = (position: ChessPosition) => {
      console.log('Chess analysis started:', position)
      setIsAnalyzing(true)
    }

    const handleAnalysisUpdate = (data: any) => {
      console.log('Chess analysis update:', data)
      if (data.position) {
        setCurrentPosition(data.position)
      }
      if (data.evaluation) {
        setEvaluation(data.evaluation)
      }
      if (data.bestMove) {
        setBestMove(data.bestMove)
      }
      if (data.highlight) {
        setMoveHighlight(data.highlight)
      }
    }

    const handleMoveHighlight = (data: any) => {
      console.log('Chess move highlight:', data)
      if (data.highlight) {
        setMoveHighlight(data.highlight)
      }
      if (data.evaluation) {
        setEvaluation(data.evaluation)
      }
      if (data.bestMove) {
        setBestMove(data.bestMove)
      }
    }

    const handleAnalysisError = (error: any) => {
      console.error('Chess analysis error:', error)
      setIsAnalyzing(false)
    }

    const handleNoScreenshots = () => {
      console.log('No chess board detected in screenshot')
      setIsAnalyzing(false)
    }

    const handleScreenshotTaken = () => {
      loadScreenshots()
    }

    const handleReset = () => {
      setCurrentPosition(null)
      setIsAnalyzing(false)
      setBestMove('')
      setEvaluation('No position')
      setMoveHighlight(null)
      loadScreenshots()
    }

    const handleMonitoringStarted = (data: any) => {
      console.log('Monitoring started:', data)
      setIsMonitoring(true)
      setMonitoringState(data)
    }

    const handleMonitoringStopped = () => {
      console.log('Monitoring stopped')
      setIsMonitoring(false)
      setMonitoringState(null)
    }

    const handleMoveSuggestion = (data: any) => {
      console.log('Move suggestion received:', data)
      if (data.bestMove) {
        setMoveHighlight(data.bestMove)
      }
      if (data.evaluation) {
        setEvaluation(data.evaluation)
      }
      if (data.moveText) {
        setBestMove(data.moveText)
      }
    }

    // Register event listeners
    window.electronAPI.onChessBoardDetected(handleBoardDetected)
    window.electronAPI.onChessAnalysisStart(handleAnalysisStart)
    window.electronAPI.onChessAnalysisUpdate(handleAnalysisUpdate)
    window.electronAPI.onChessMoveHighlight(handleMoveHighlight)
    window.electronAPI.onChessAnalysisError(handleAnalysisError)
    window.electronAPI.onChessNoScreenshots(handleNoScreenshots)
    window.electronAPI.onScreenshotTaken(handleScreenshotTaken)
    window.electronAPI.onChessReset(handleReset)

    // Register real-time monitoring event listeners
    window.electronAPI.onRealtimeMonitoringStarted?.(handleMonitoringStarted)
    window.electronAPI.onRealtimeMonitoringStopped?.(handleMonitoringStopped)
    window.electronAPI.onRealtimeMoveSuggestion?.(handleMoveSuggestion)

    // Load initial screenshots
    loadScreenshots()

    return () => {
      // Cleanup listeners
      window.electronAPI.removeAllListeners?.()
    }
  }, [loadScreenshots])

  return (
    <div className="min-h-screen bg-black/90 backdrop-blur-sm text-white p-4">
      <div className="max-w-md mx-auto space-y-4">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="w-6 h-6 text-blue-400" />
            <h1 className="text-xl font-bold">Chessly</h1>
            <span className="text-xs bg-blue-600 px-2 py-1 rounded-full">v1.0</span>
          </div>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowInstructions(true)}
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
              title="How to Use"
            >
              <HelpCircle className="w-4 h-4" />
            </button>
            <button
              onClick={toggleVisibility}
              className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
              title="Toggle Visibility"
            >
              {isVisible ? <Eye className="w-4 h-4" /> : <EyeOff className="w-4 h-4" />}
            </button>
          </div>
        </div>

        {/* Status */}
        <div className="bg-white/5 rounded-lg p-4 space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Status:</span>
            <span className={cn(
              "text-sm font-medium",
              isMonitoring ? "text-green-400" : isAnalyzing ? "text-yellow-400" : currentPosition ? "text-blue-400" : "text-white/60"
            )}>
              {isMonitoring ? "🔴 Live Monitoring" : isAnalyzing ? "Analyzing..." : currentPosition ? "Ready" : "Waiting..."}
            </span>
          </div>

          {monitoringState && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-white/60">Playing as:</span>
              <span className="text-sm font-medium text-purple-400">
                {monitoringState.playerColor} on {monitoringState.website}
              </span>
            </div>
          )}
          
          <div className="flex items-center justify-between">
            <span className="text-sm text-white/60">Evaluation:</span>
            <span className="text-sm font-medium text-blue-400">{evaluation}</span>
          </div>
          
          {bestMove && (
            <div className="flex items-center justify-between">
              <span className="text-sm text-white/60">Best Move:</span>
              <span className="text-sm font-medium text-green-400">{bestMove}</span>
            </div>
          )}
        </div>

        {/* Real-time Controls */}
        <div className="bg-white/5 rounded-lg p-4">
          <div className="flex items-center justify-between mb-3">
            <h3 className="text-sm font-medium text-white/80">🚀 Real-time Mode</h3>
            <button
              onClick={() => setShowInstructions(true)}
              className="text-xs text-blue-400 hover:text-blue-300 underline"
            >
              How it works?
            </button>
          </div>

          {!isMonitoring && (
            <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-3 mb-3">
              <p className="text-xs text-blue-300 mb-2">
                📋 <strong>Quick Setup:</strong>
              </p>
              <ol className="text-xs text-blue-200 space-y-1">
                <li>1. Open chess.com or lichess.org</li>
                <li>2. Start a chess game</li>
                <li>3. Click your color below</li>
              </ol>
            </div>
          )}

          <div className="grid grid-cols-2 gap-2 mb-3">
            <button
              onClick={() => startQuickMode('white')}
              disabled={isMonitoring}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
            >
              <span className="text-lg">⚪</span>
              <span className="text-xs">Play White</span>
            </button>

            <button
              onClick={() => startQuickMode('black')}
              disabled={isMonitoring}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
            >
              <span className="text-lg">⚫</span>
              <span className="text-xs">Play Black</span>
            </button>
          </div>

          <div className="grid grid-cols-2 gap-2">
            {isMonitoring ? (
              <button
                onClick={stopMonitoring}
                className="flex flex-col items-center gap-1 p-3 rounded-lg bg-red-600 hover:bg-red-700 transition-colors"
              >
                <Square className="w-5 h-5" />
                <span className="text-xs">Stop</span>
              </button>
            ) : (
              <button
                onClick={() => {
                  const color = prompt('Are you playing as white or black?', 'white')
                  if (color === 'white' || color === 'black') {
                    startMonitoring(color)
                  }
                }}
                className="flex flex-col items-center gap-1 p-3 rounded-lg bg-green-600 hover:bg-green-700 transition-colors"
              >
                <Play className="w-5 h-5" />
                <span className="text-xs">Start Live</span>
              </button>
            )}

            <button
              onClick={simulateGame}
              disabled={!isMonitoring}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-purple-600 hover:bg-purple-700 transition-colors disabled:opacity-50"
            >
              <Zap className="w-5 h-5" />
              <span className="text-xs">Demo</span>
            </button>
          </div>
        </div>

        {/* Manual Controls */}
        <div className="bg-white/5 rounded-lg p-4">
          <h3 className="text-sm font-medium text-white/80 mb-3">📷 Manual Mode</h3>
          <div className="grid grid-cols-3 gap-2">
            <button
              onClick={takeScreenshot}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-blue-600 hover:bg-blue-700 transition-colors"
            >
              <Camera className="w-5 h-5" />
              <span className="text-xs">Capture</span>
            </button>

            <button
              onClick={resetAnalysis}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-red-600 hover:bg-red-700 transition-colors"
            >
              <RotateCcw className="w-5 h-5" />
              <span className="text-xs">Reset</span>
            </button>

            <button
              onClick={toggleVisibility}
              className="flex flex-col items-center gap-1 p-3 rounded-lg bg-gray-600 hover:bg-gray-700 transition-colors"
            >
              {isVisible ? <Eye className="w-5 h-5" /> : <EyeOff className="w-5 h-5" />}
              <span className="text-xs">Toggle</span>
            </button>
          </div>
        </div>

        {/* Screenshots */}
        {screenshots.length > 0 && (
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-2">Recent Screenshots</h3>
            <div className="grid grid-cols-2 gap-2">
              {screenshots.slice(-4).map((screenshot, index) => (
                <div key={screenshot.path} className="relative">
                  <img
                    src={screenshot.preview}
                    alt={`Screenshot ${index + 1}`}
                    className="w-full h-20 object-cover rounded border border-white/10"
                  />
                  {index === screenshots.length - 1 && currentPosition && (
                    <div className="absolute top-1 right-1 w-2 h-2 bg-green-400 rounded-full"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Position Info */}
        {currentPosition && (
          <div className="bg-white/5 rounded-lg p-4">
            <h3 className="text-sm font-medium text-white/80 mb-2">Position Details</h3>
            <div className="space-y-1 text-xs text-white/60">
              <div>FEN: {currentPosition.fen.substring(0, 30)}...</div>
              <div>Confidence: {(currentPosition.boardDetection?.confidence * 100 || 0).toFixed(1)}%</div>
              <div>Player: {currentPosition.boardDetection?.playerColor || 'Unknown'}</div>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-white/5 rounded-lg p-4">
          <h3 className="text-sm font-medium text-white/80 mb-2">Quick Guide</h3>
          <div className="space-y-1 text-xs text-white/60">
            <div>• Ctrl+H: Take screenshot</div>
            <div>• Ctrl+B: Toggle visibility</div>
            <div>• Ctrl+R: Reset analysis</div>
            <div>• Ctrl+Q: Quit application</div>
          </div>
        </div>

        {/* Instructions Dialog */}
        <InstructionsDialog
          isOpen={showInstructions}
          onClose={() => setShowInstructions(false)}
        />
      </div>
    </div>
  )
}
