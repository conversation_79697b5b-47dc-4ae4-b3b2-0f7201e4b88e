import { QueryProvider } from './providers/query-provider'
import { useCallback, useEffect, useState } from 'react'
import { ChessApp } from './components/chess-app'
import { ToastContext } from '@renderer/providers/toast-context'
import {
  Toast,
  ToastProvider,
  ToastViewport,
  ToastDescription,
  ToastTitle
} from './components/ui/toast'

interface AppConfig {
  opacity?: number
  autoAnalyze?: boolean
  showSquareNames?: boolean
  highlightColor?: string
}

function App(): React.JSX.Element {
  const [isInitialized, setIsInitialized] = useState(false)
  const [toastState, setToastState] = useState({
    open: false,
    title: '',
    description: '',
    variant: 'neutral' as 'neutral' | 'success' | 'error'
  })

  const markInitialized = useCallback(() => {
    setIsInitialized(true)
    window.__IS_INITIALIZED__ = true
  }, [])

  const showToast = useCallback(
    (title: string, description: string, variant: 'neutral' | 'success' | 'error') => {
      setToastState({
        open: true,
        title,
        description,
        variant
      })
    },
    []
  )

  useEffect(() => {
    const initializeApp = async () => {
      try {
        const config = (await window.electronAPI.getConfig()) as AppConfig
        console.log('Chessly initialized with config:', config)
        markInitialized()
      } catch (error) {
        console.error('Error initializing Chessly:', error)
        markInitialized()
      }
    }
    initializeApp()

    return () => {
      window.__IS_INITIALIZED__ = false
      setIsInitialized(false)
    }
  }, [markInitialized])

  return (
    <QueryProvider>
      <ToastProvider>
        <ToastContext.Provider value={{ showToast }}>
          <div className="relative">
            {isInitialized ? (
              <ChessApp />
            ) : (
              <div className="min-h-screen bg-black flex items-center justify-center">
                <div className="flex flex-col items-center gap-3">
                  <div className="w-6 h-6 border-2 border-white/20 border-t-white/80 rounded-full animate-spin"></div>
                  <p className="text-white/60 text-sm">Initializing Chessly...</p>
                </div>
              </div>
            )}
          </div>
          <Toast
            open={toastState.open}
            onOpenChange={(open) => setToastState((prev) => ({ ...prev, open }))}
            variant={toastState.variant}
            duration={1000}
          >
            <ToastTitle>{toastState.title}</ToastTitle>
            <ToastDescription>{toastState.description}</ToastDescription>
          </Toast>
          <ToastViewport />
        </ToastContext.Provider>
      </ToastProvider>
    </QueryProvider>
  )
}

export default App
