export interface ElectronAPI {
  getConfig: () => Promise<{
    opacity?: number
    autoAnalyze?: boolean
    showSquareNames?: boolean
    highlightColor?: string
  }>
  updateConfig: (config: {
    opacity?: number
    autoAnalyze?: boolean
    showSquareNames?: boolean
    highlightColor?: string
  }) => Promise<boolean>
  getScreenshots: () => Promise<{ path: string; preview: string }[]>
  deleteScreenshot: (path: string) => Promise<{ success: boolean; error?: string }>
  onScreenshotTaken: (callback: (data: { path: string; preview: string }) => void) => () => void
  toggleMainWindow: () => Promise<{ success: boolean; error?: string }>
  getPlatform: () => string
  triggerScreenshot: () => Promise<{ success: boolean; error?: string }>
  deleteLastScreenshot: () => Promise<{ success: boolean; error?: string }>
  triggerReset: () => Promise<{ success: boolean; error?: string }>

  // Chess-specific API methods
  getCurrentPosition: () => Promise<any>
  getIsAnalyzing: () => Promise<boolean>
  stopChessAnalysis: () => Promise<{ success: boolean }>
  getBestMove: () => Promise<any>
  getEvaluation: () => Promise<string>
  getSquareInfo: (file: string, rank: number) => Promise<any>
  getAllSquares: () => Promise<string[]>
  triggerChessAnalysis: () => Promise<{ success: boolean; error?: string }>

  // Real-time monitoring API methods
  startRealtimeMonitoring: (playerColor: 'white' | 'black') => Promise<{ success: boolean; error?: string }>
  stopRealtimeMonitoring: () => Promise<{ success: boolean }>
  getMonitoringState: () => Promise<any>
  startQuickMode: (playerColor: 'white' | 'black') => Promise<{ success: boolean; error?: string }>
  simulateGame: () => Promise<{ success: boolean; error?: string }>
  analyzeManualPosition: (fen: string) => Promise<{ success: boolean; error?: string }>

  // Chess event handlers
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChessBoardDetected: (callback: (position: any) => void) => () => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChessAnalysisStart: (callback: (position: any) => void) => () => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChessAnalysisUpdate: (callback: (data: any) => void) => () => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChessMoveHighlight: (callback: (data: any) => void) => () => void
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChessAnalysisError: (callback: (error: any) => void) => () => void
  onChessNoScreenshots: (callback: () => void) => () => void
  onChessReset: (callback: () => void) => () => void

  // Real-time monitoring event handlers
  onRealtimeMonitoringStarted?: (callback: (data: any) => void) => () => void
  onRealtimeMonitoringStopped?: (callback: () => void) => () => void
  onRealtimeMoveSuggestion?: (callback: (data: any) => void) => () => void
  updateContentDimensions: (dimensions: { width: number; height: number }) => void
  openLink: (url: string) => Promise<{ success: boolean; error?: string }>
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  removeListener: (eventName: string, callback: (...args: any[]) => void) => void
  removeAllListeners: () => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
    __IS_INITIALIZED__: boolean
  }
}
