import { BrowserWindow } from 'electron'
import { ChessManager } from './chess-manager'
import { BrowserIntegration } from './browser-integration'

export interface MonitoringState {
  isActive: boolean
  website: string | null
  playerColor: 'white' | 'black' | null
  currentFen: string | null
  lastMoveTime: number
  analysisCount: number
}

export interface MonitoringOptions {
  pollInterval?: number
  autoStart?: boolean
  enableNotifications?: boolean
}

export class RealtimeMonitor {
  private chessManager: ChessManager
  private browserIntegration: BrowserIntegration
  private mainWindow: BrowserWindow | null = null
  private state: MonitoringState = {
    isActive: false,
    website: null,
    playerColor: null,
    currentFen: null,
    lastMoveTime: 0,
    analysisCount: 0
  }

  constructor(chessManager: ChessManager, options: MonitoringOptions = {}) {
    this.chessManager = chessManager
    
    this.browserIntegration = new BrowserIntegration({
      pollInterval: options.pollInterval || 1000, // Check every second
      chessManager: this.chessManager,
      onPositionChange: this.handlePositionChange.bind(this),
      onError: this.handleError.bind(this)
    })
  }

  public setMainWindow(window: BrowserWindow): void {
    this.mainWindow = window
  }

  public async startMonitoring(): Promise<{ success: boolean; error?: string }> {
    try {
      if (this.state.isActive) {
        return { success: false, error: 'Monitoring already active' }
      }

      console.log('🎯 Starting Chessly real-time monitoring...')

      // Start browser integration
      const result = await this.browserIntegration.startMonitoring()
      if (!result.success) {
        return result
      }

      // Update state
      this.state.isActive = true
      this.state.website = this.browserIntegration.getCurrentWebsite()
      this.state.playerColor = this.browserIntegration.getCurrentPlayerColor()
      this.state.analysisCount = 0

      // Notify renderer
      this.notifyRenderer('monitoring-started', {
        website: this.state.website,
        playerColor: this.state.playerColor
      })

      console.log(`✅ Monitoring started for ${this.state.website} as ${this.state.playerColor}`)
      return { success: true }
    } catch (error) {
      console.error('Error starting monitoring:', error)
      return { success: false, error: 'Failed to start monitoring' }
    }
  }

  public stopMonitoring(): void {
    if (!this.state.isActive) {
      return
    }

    console.log('🛑 Stopping Chessly monitoring...')

    // Stop browser integration
    this.browserIntegration.stopMonitoring()

    // Reset state
    this.state = {
      isActive: false,
      website: null,
      playerColor: null,
      currentFen: null,
      lastMoveTime: 0,
      analysisCount: 0
    }

    // Notify renderer
    this.notifyRenderer('monitoring-stopped', {})

    console.log('✅ Monitoring stopped')
  }

  public getState(): MonitoringState {
    return { ...this.state }
  }

  public isMonitoring(): boolean {
    return this.state.isActive
  }

  private async handlePositionChange(fen: string, website: string): Promise<void> {
    try {
      console.log(`🔄 Position changed on ${website}:`, fen)

      // Update state
      this.state.currentFen = fen
      this.state.lastMoveTime = Date.now()
      this.state.analysisCount++

      // Determine player color for analysis
      const playerColor = this.state.playerColor === 'white' ? 'w' : 'b'

      // Analyze the new position
      const position = await this.chessManager.analyzePosition(
        fen,
        playerColor,
        (analysis) => {
          // Send analysis to renderer
          this.notifyRenderer('analysis-update', {
            analysis,
            position: this.state,
            bestMove: analysis.bestMove,
            evaluation: this.formatEvaluation(analysis)
          })
        },
        (position) => {
          // Send position update to renderer
          this.notifyRenderer('position-update', {
            position,
            state: this.state
          })
        }
      )

      if (position) {
        console.log(`📊 Analysis #${this.state.analysisCount} completed`)
        
        // Get best move for display
        const bestMove = this.chessManager.getBestMoveText()
        const evaluation = this.chessManager.getEvaluationText()
        
        console.log(`💡 ${bestMove}`)
        console.log(`📈 ${evaluation}`)

        // Send comprehensive update to renderer
        this.notifyRenderer('move-suggestion', {
          bestMove: this.chessManager.getBestMoveHighlight(),
          evaluation,
          moveText: bestMove,
          fen,
          analysisCount: this.state.analysisCount,
          website: this.state.website,
          playerColor: this.state.playerColor
        })
      }
    } catch (error) {
      console.error('Error handling position change:', error)
      this.handleError('Failed to analyze position')
    }
  }

  private handleError(error: string): void {
    console.error('🚨 Monitoring error:', error)
    
    this.notifyRenderer('monitoring-error', {
      error,
      timestamp: Date.now()
    })
  }

  private formatEvaluation(analysis: any): string {
    if (!analysis) return 'Analyzing...'
    
    if (analysis.mate !== undefined) {
      const mateIn = Math.abs(analysis.mate)
      const side = analysis.mate > 0 ? 'White' : 'Black'
      return `${side} mates in ${mateIn}`
    }

    const evaluation = analysis.evaluation || 0
    if (Math.abs(evaluation) < 0.1) {
      return 'Equal position'
    } else if (evaluation > 0) {
      return `White +${evaluation.toFixed(1)}`
    } else {
      return `Black +${Math.abs(evaluation).toFixed(1)}`
    }
  }

  private notifyRenderer(event: string, data: any): void {
    if (this.mainWindow && !this.mainWindow.isDestroyed()) {
      this.mainWindow.webContents.send(`realtime-${event}`, data)
    }
  }

  // Manual position input for testing
  public async analyzeManualPosition(fen: string): Promise<void> {
    if (!this.state.isActive) {
      console.log('Starting manual analysis mode...')
      this.state.isActive = true
      this.state.website = 'Manual Input'
      this.state.playerColor = 'white'
    }

    await this.handlePositionChange(fen, 'Manual Input')
  }

  // Quick start for testing without browser integration
  public async startQuickMode(playerColor: 'white' | 'black' = 'white'): Promise<void> {
    console.log('🚀 Starting Chessly in quick mode...')

    this.state.isActive = true
    this.state.website = 'Quick Mode'
    this.state.playerColor = playerColor
    this.state.analysisCount = 0

    // Analyze starting position
    const startingFen = 'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1'
    await this.analyzeManualPosition(startingFen)

    this.notifyRenderer('monitoring-started', {
      website: this.state.website,
      playerColor: this.state.playerColor,
      mode: 'quick'
    })

    console.log(`✅ Quick mode started as ${playerColor}`)
  }

  // Simulate position changes for testing
  public async simulateGame(): Promise<void> {
    if (!this.state.isActive) {
      await this.startQuickMode()
    }

    const testPositions = [
      'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
      'rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1',
      'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2',
      'rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2',
      'rnbqkb1r/pppp1ppp/5n2/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R w KQkq - 2 3'
    ]

    console.log('🎮 Simulating chess game...')

    for (let i = 0; i < testPositions.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 3000)) // Wait 3 seconds between moves
      await this.analyzeManualPosition(testPositions[i])
    }
  }

  public destroy(): void {
    this.stopMonitoring()
    this.chessManager.destroy()
  }
}
