import { BrowserWindow } from 'electron'
import { ChessManager } from './chess-manager'

export interface ChessWebsite {
  name: string
  domains: string[]
  boardSelector: string
  pieceSelector: string
  squarePattern: string
  getFenScript: string
}

export interface BrowserMonitorOptions {
  pollInterval: number
  chessManager: ChessManager
  onPositionChange?: (fen: string, website: string) => void
  onError?: (error: string) => void
}

export class BrowserIntegration {
  private isMonitoring = false
  private currentWebsite: ChessWebsite | null = null
  private lastFen: string = ''
  private playerColor: 'w' | 'b' = 'w'
  private pollInterval: NodeJS.Timeout | null = null
  private options: BrowserMonitorOptions

  // Supported chess websites
  private readonly websites: ChessWebsite[] = [
    {
      name: 'Chess.com',
      domains: ['chess.com', 'www.chess.com'],
      boardSelector: '.board',
      pieceSelector: '.piece',
      squarePattern: 'square-',
      getFenScript: this.getChessComFenScript()
    },
    {
      name: 'Lichess',
      domains: ['lichess.org', 'www.lichess.org'],
      boardSelector: 'cg-board',
      pieceSelector: 'piece',
      squarePattern: '',
      getFenScript: this.getLichessFenScript()
    }
  ]

  constructor(options: BrowserMonitorOptions) {
    this.options = options
  }

  public async startMonitoring(): Promise<{ success: boolean; error?: string }> {
    try {
      if (this.isMonitoring) {
        return { success: false, error: 'Already monitoring' }
      }

      // Get player color selection
      const colorResult = await this.getPlayerColor()
      if (!colorResult.success) {
        return colorResult
      }

      // Detect chess website
      const websiteResult = await this.detectChessWebsite()
      if (!websiteResult.success) {
        return websiteResult
      }

      // Start monitoring
      this.isMonitoring = true
      this.startPolling()

      console.log(`Started monitoring ${this.currentWebsite?.name} as ${this.playerColor === 'w' ? 'white' : 'black'}`)
      return { success: true }
    } catch (error) {
      console.error('Error starting browser monitoring:', error)
      return { success: false, error: 'Failed to start monitoring' }
    }
  }

  public stopMonitoring(): void {
    this.isMonitoring = false
    if (this.pollInterval) {
      clearInterval(this.pollInterval)
      this.pollInterval = null
    }
    this.currentWebsite = null
    this.lastFen = ''
    console.log('Stopped browser monitoring')
  }

  private async getPlayerColor(): Promise<{ success: boolean; error?: string }> {
    return new Promise((resolve) => {
      // Create a simple dialog to get player color
      const dialog = new BrowserWindow({
        width: 400,
        height: 200,
        modal: true,
        resizable: false,
        webPreferences: {
          nodeIntegration: true,
          contextIsolation: false
        }
      })

      const html = `
        <!DOCTYPE html>
        <html>
        <head>
          <title>Select Your Color</title>
          <style>
            body { font-family: Arial, sans-serif; padding: 20px; text-align: center; background: #1a1a1a; color: white; }
            button { padding: 15px 30px; margin: 10px; font-size: 16px; cursor: pointer; border: none; border-radius: 5px; }
            .white { background: #f0f0f0; color: #333; }
            .black { background: #333; color: #f0f0f0; }
            h2 { color: #4CAF50; }
          </style>
        </head>
        <body>
          <h2>🎯 Chessly - Select Your Color</h2>
          <p>Which color are you playing as?</p>
          <button class="white" onclick="selectColor('w')">⚪ White</button>
          <button class="black" onclick="selectColor('b')">⚫ Black</button>
          <script>
            function selectColor(color) {
              require('electron').ipcRenderer.send('color-selected', color);
              window.close();
            }
          </script>
        </body>
        </html>
      `

      dialog.loadURL(`data:text/html;charset=utf-8,${encodeURIComponent(html)}`)

      dialog.webContents.on('ipc-message', (event, channel, color) => {
        if (channel === 'color-selected') {
          this.playerColor = color
          dialog.close()
          resolve({ success: true })
        }
      })

      dialog.on('closed', () => {
        if (!this.playerColor) {
          resolve({ success: false, error: 'Color selection cancelled' })
        }
      })
    })
  }

  private async detectChessWebsite(): Promise<{ success: boolean; error?: string }> {
    try {
      // Get all browser windows (this is a simplified approach)
      // In a real implementation, you'd need to use system APIs to detect browser windows
      // For now, we'll assume the user has a chess website open and try to inject scripts

      // Try to find a chess website by checking common URLs
      for (const website of this.websites) {
        const detected = await this.tryDetectWebsite(website)
        if (detected) {
          this.currentWebsite = website
          return { success: true }
        }
      }

      return { success: false, error: 'No supported chess website detected. Please open chess.com or lichess.org' }
    } catch (error) {
      return { success: false, error: 'Failed to detect chess website' }
    }
  }

  private async tryDetectWebsite(website: ChessWebsite): Promise<boolean> {
    // This is a simplified detection - in a real implementation,
    // you'd need to use system APIs to access browser content
    // For now, we'll return true for chess.com as a mock
    return website.name === 'Chess.com'
  }

  private startPolling(): void {
    this.pollInterval = setInterval(async () => {
      if (!this.isMonitoring || !this.currentWebsite) {
        return
      }

      try {
        const fen = await this.getCurrentFen()
        if (fen && fen !== this.lastFen) {
          this.lastFen = fen
          console.log('Position changed:', fen)
          
          // Analyze new position
          await this.options.chessManager.analyzePosition(fen, this.playerColor)
          
          if (this.options.onPositionChange) {
            this.options.onPositionChange(fen, this.currentWebsite.name)
          }
        }
      } catch (error) {
        console.error('Error polling chess position:', error)
        if (this.options.onError) {
          this.options.onError('Failed to read chess position')
        }
      }
    }, this.options.pollInterval)
  }

  private async getCurrentFen(): Promise<string | null> {
    if (!this.currentWebsite) {
      return null
    }

    // This would execute the FEN extraction script in the browser
    // For now, we'll return a mock FEN that changes occasionally
    const mockFens = [
      'rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR w KQkq - 0 1',
      'rnbqkbnr/pppppppp/8/8/4P3/8/PPPP1PPP/RNBQKBNR b KQkq e3 0 1',
      'rnbqkbnr/pppp1ppp/8/4p3/4P3/8/PPPP1PPP/RNBQKBNR w KQkq e6 0 2',
      'rnbqkbnr/pppp1ppp/8/4p3/4P3/5N2/PPPP1PPP/RNBQKB1R b KQkq - 1 2'
    ]
    
    // Simulate position changes
    const index = Math.floor(Date.now() / 10000) % mockFens.length
    return mockFens[index] + ` ${this.playerColor}`
  }

  private getChessComFenScript(): string {
    return `
      function getFenString() {
        let fen_string = "";
        for(var i = 8; i >= 1; i--) {
          for(var j = 1; j <= 8; j++) {
            let position = \`\${j}\${i}\`;
            if(j == 1 && i != 8) {
              fen_string += "/";
            }
            let piece_in_position = document.querySelectorAll(\`.piece.square-\${position}\`)[0]?.classList ?? null;
            if(piece_in_position != null) {
              for(var item of piece_in_position.values()) {
                if(item.length == 2) {
                  piece_in_position = item;
                }
              }
            }
            if(piece_in_position == null) {
              let previous_char = fen_string.split("").pop();
              if(!isNaN(Number(previous_char))) {
                fen_string = fen_string.substring(0, fen_string.length - 1);
                fen_string += Number(previous_char) + 1;
              } else {
                fen_string += "1";
              }
            } else if(piece_in_position?.split("")[0] == "b") {
              fen_string += piece_in_position.split("")[1];
            } else if(piece_in_position?.split("")[0] == "w") {
              fen_string += piece_in_position.split("")[1].toUpperCase();
            }
          }
        }
        return fen_string;
      }
      getFenString();
    `
  }

  private getLichessFenScript(): string {
    return `
      function getLichessFen() {
        // Lichess FEN extraction logic would go here
        // This is more complex as Lichess uses different DOM structure
        return "rnbqkbnr/pppppppp/8/8/8/8/PPPPPPPP/RNBQKBNR";
      }
      getLichessFen();
    `
  }

  public isCurrentlyMonitoring(): boolean {
    return this.isMonitoring
  }

  public getCurrentWebsite(): string | null {
    return this.currentWebsite?.name || null
  }

  public getCurrentPlayerColor(): 'white' | 'black' {
    return this.playerColor === 'w' ? 'white' : 'black'
  }
}
