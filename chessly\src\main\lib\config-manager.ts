import { app } from 'electron'
import { EventEmitter } from 'events'
import path from 'path'
import fs from 'fs'

interface Config {
  opacity: number
  autoAnalyze: boolean
  showSquareNames: boolean
  highlightColor: string
}

export class ConfigManager extends EventEmitter {
  private configPath: string
  private defaultConfig: Config = {
    opacity: 0.9,
    autoAnalyze: true,
    showSquareNames: false,
    highlightColor: '#3b82f6'
  }

  constructor() {
    super()
    try {
      this.configPath = path.join(app.getPath('userData'), 'config.json')
      console.log('Config path:', this.configPath)
    } catch (error) {
      console.error('Error getting config path:', error)
      this.configPath = path.join(process.cwd(), 'config.json')
    }

    this.ensureConfigFileExists()
  }

  private ensureConfigFileExists(): void {
    try {
      if (!fs.existsSync(this.configPath)) {
        this.saveConfig(this.defaultConfig)
        console.log('Created default config file:', this.configPath)
      }
    } catch (error) {
      console.error('Failed to ensure config file exists:', error)
    }
  }

  public saveConfig(config: Config): void {
    try {
      const configDir = path.dirname(this.configPath)
      if (!fs.existsSync(configDir)) {
        fs.mkdirSync(configDir, { recursive: true })
      }
      fs.writeFileSync(this.configPath, JSON.stringify(config, null, 2))
    } catch (error) {
      console.error('Failed to save config:', error)
    }
  }

  public loadConfig(): Config {
    try {
      if (fs.existsSync(this.configPath)) {
        const configData = fs.readFileSync(this.configPath, 'utf-8')
        const config = JSON.parse(configData)
        return {
          ...this.defaultConfig,
          ...config
        }
      }
      this.saveConfig(this.defaultConfig)
      return this.defaultConfig
    } catch (error) {
      console.error('Failed to load config:', error)
      return this.defaultConfig
    }
  }

  public updateConfig(updates: Partial<Config>): Config {
    try {
      const currentConfig = this.loadConfig()
      const newConfig = {
        ...currentConfig,
        ...updates
      }
      this.saveConfig(newConfig)
      this.emit('config-updated', newConfig)
      return newConfig
    } catch (error) {
      console.error('Failed to update config:', error)
      return this.defaultConfig
    }
  }

  public getOpacity(): number {
    const config = this.loadConfig()
    return config.opacity !== undefined ? config.opacity : 0.9
  }

  public setOpacity(opacity: number): void {
    const validOpacity = Math.min(1.0, Math.max(0.1, opacity))
    this.updateConfig({ opacity: validOpacity })
  }

  public getAutoAnalyze(): boolean {
    const config = this.loadConfig()
    return config.autoAnalyze !== undefined ? config.autoAnalyze : true
  }

  public setAutoAnalyze(autoAnalyze: boolean): void {
    this.updateConfig({ autoAnalyze })
  }

  public getShowSquareNames(): boolean {
    const config = this.loadConfig()
    return config.showSquareNames !== undefined ? config.showSquareNames : false
  }

  public setShowSquareNames(showSquareNames: boolean): void {
    this.updateConfig({ showSquareNames })
  }

  public getHighlightColor(): string {
    const config = this.loadConfig()
    return config.highlightColor !== undefined ? config.highlightColor : '#3b82f6'
  }

  public setHighlightColor(highlightColor: string): void {
    this.updateConfig({ highlightColor })
  }
}

export const configManager = new ConfigManager()
