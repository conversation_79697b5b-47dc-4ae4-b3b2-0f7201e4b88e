import { Chess<PERSON>ngine, Chess<PERSON>nalysis, Chess<PERSON>ove } from './chess-engine'
import { ChessBoardDetector, ChessBoardDetection } from './chess-board-detector'
import { <PERSON><PERSON><PERSON>Window } from 'electron'

export interface ChessPosition {
  fen: string
  boardDetection: ChessBoardDetection
  analysis?: ChessAnalysis
  timestamp: number
}

export interface MoveHighlight {
  from: { x: number; y: number; width: number; height: number }
  to: { x: number; y: number; width: number; height: number }
  move: ChessMove
}

export class ChessManager {
  private engine: ChessEngine
  private detector: ChessBoardDetector
  private currentPosition: ChessPosition | null = null
  private analysisCallback: ((analysis: ChessAnalysis) => void) | null = null
  private positionCallback: ((position: ChessPosition) => void) | null = null
  private isAnalyzing = false

  constructor() {
    this.engine = new ChessEngine()
    this.detector = new ChessBoardDetector()
  }

  public async analyzeScreenshot(
    screenshotPath: string,
    onAnalysis?: (analysis: ChessAnalysis) => void,
    onPosition?: (position: ChessPosition) => void
  ): Promise<ChessPosition | null> {
    try {
      console.log('Analyzing chess screenshot:', screenshotPath)

      // Detect chess board in screenshot
      const boardDetection = await this.detector.detectChessBoard(screenshotPath)
      if (!boardDetection) {
        console.log('No chess board detected in screenshot')
        return null
      }

      console.log('Chess board detected with confidence:', boardDetection.confidence)
      console.log('Detected FEN:', boardDetection.fen)

      return await this.analyzePosition(boardDetection.fen, 'w', onAnalysis, onPosition, boardDetection)
    } catch (error) {
      console.error('Error analyzing chess screenshot:', error)
      return null
    }
  }

  public async analyzePosition(
    fen: string,
    playerColor: 'w' | 'b' = 'w',
    onAnalysis?: (analysis: ChessAnalysis) => void,
    onPosition?: (position: ChessPosition) => void,
    boardDetection?: any
  ): Promise<ChessPosition | null> {
    try {
      console.log('Analyzing chess position:', fen)

      // Create position object
      const position: ChessPosition = {
        fen,
        boardDetection: boardDetection || {
          squares: [],
          fen,
          boardBounds: { x: 0, y: 0, width: 480, height: 480 },
          playerColor: playerColor === 'w' ? 'white' : 'black',
          confidence: 1.0
        },
        timestamp: Date.now()
      }

      // Set callbacks
      this.analysisCallback = onAnalysis || null
      this.positionCallback = onPosition || null

      // Set position in engine
      const validPosition = this.engine.setPosition(fen)
      if (!validPosition) {
        console.error('Invalid chess position:', fen)
        return null
      }

      // Store current position
      this.currentPosition = position

      // Notify about position detection
      if (this.positionCallback) {
        this.positionCallback(position)
      }

      // Start analysis
      this.isAnalyzing = true
      this.engine.analyzePosition((analysis) => {
        if (this.currentPosition) {
          this.currentPosition.analysis = analysis

          // Notify about analysis update
          if (this.analysisCallback) {
            this.analysisCallback(analysis)
          }
        }
      })

      return position
    } catch (error) {
      console.error('Error analyzing chess position:', error)
      return null
    }
  }

  public getCurrentPosition(): ChessPosition | null {
    return this.currentPosition
  }

  public getBestMoveHighlight(): MoveHighlight | null {
    if (!this.currentPosition?.analysis?.bestMove || !this.currentPosition.boardDetection) {
      return null
    }

    const { bestMove } = this.currentPosition.analysis
    const { squares } = this.currentPosition.boardDetection

    // Find the squares for the move
    const fromSquare = squares.find(s => 
      s.file === bestMove.from[0] && s.rank === parseInt(bestMove.from[1])
    )
    const toSquare = squares.find(s => 
      s.file === bestMove.to[0] && s.rank === parseInt(bestMove.to[1])
    )

    if (!fromSquare || !toSquare) {
      return null
    }

    return {
      from: {
        x: fromSquare.x,
        y: fromSquare.y,
        width: fromSquare.width,
        height: fromSquare.height
      },
      to: {
        x: toSquare.x,
        y: toSquare.y,
        width: toSquare.width,
        height: toSquare.height
      },
      move: bestMove
    }
  }

  public getSquareInfo(file: string, rank: number): { name: string; piece: string | null } | null {
    if (!this.currentPosition?.boardDetection) {
      return null
    }

    const square = this.currentPosition.boardDetection.squares.find(s => 
      s.file === file && s.rank === rank
    )

    if (!square) {
      return null
    }

    return {
      name: `${file}${rank}`,
      piece: square.piece
    }
  }

  public getAllSquareNames(): string[] {
    const squares: string[] = []
    for (let file = 0; file < 8; file++) {
      for (let rank = 1; rank <= 8; rank++) {
        squares.push(`${String.fromCharCode(97 + file)}${rank}`)
      }
    }
    return squares
  }

  public getEvaluationText(): string {
    if (!this.currentPosition?.analysis) {
      return 'Analyzing...'
    }

    const { analysis } = this.currentPosition
    
    if (analysis.mate !== undefined) {
      const mateIn = Math.abs(analysis.mate)
      const side = analysis.mate > 0 ? 'White' : 'Black'
      return `${side} mates in ${mateIn}`
    }

    const evaluation = analysis.evaluation
    if (Math.abs(evaluation) < 0.1) {
      return 'Equal position'
    } else if (evaluation > 0) {
      return `White +${evaluation.toFixed(1)}`
    } else {
      return `Black +${Math.abs(evaluation).toFixed(1)}`
    }
  }

  public getBestMoveText(): string {
    if (!this.currentPosition?.analysis?.bestMove) {
      return 'Calculating...'
    }

    const { bestMove } = this.currentPosition.analysis
    return `Best move: ${bestMove.san} (${bestMove.from}-${bestMove.to})`
  }

  public stopAnalysis(): void {
    this.isAnalyzing = false
    this.engine.stopAnalysis()
    this.analysisCallback = null
    this.positionCallback = null
  }

  public isCurrentlyAnalyzing(): boolean {
    return this.isAnalyzing
  }

  public sendMoveHighlightToRenderer(mainWindow: BrowserWindow): void {
    const highlight = this.getBestMoveHighlight()
    if (highlight && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('chess-move-highlight', {
        highlight,
        evaluation: this.getEvaluationText(),
        bestMove: this.getBestMoveText(),
        position: this.currentPosition
      })
    }
  }

  public sendAnalysisToRenderer(mainWindow: BrowserWindow): void {
    if (this.currentPosition && mainWindow && !mainWindow.isDestroyed()) {
      mainWindow.webContents.send('chess-analysis-update', {
        position: this.currentPosition,
        evaluation: this.getEvaluationText(),
        bestMove: this.getBestMoveText(),
        highlight: this.getBestMoveHighlight()
      })
    }
  }

  public clearPosition(): void {
    this.stopAnalysis()
    this.currentPosition = null
  }

  public destroy(): void {
    this.stopAnalysis()
    this.engine.destroy()
  }

  // Utility methods for chess notation
  public static squareToCoordinates(square: string): { file: string; rank: number } | null {
    if (square.length !== 2) return null
    
    const file = square[0]
    const rank = parseInt(square[1])
    
    if (file < 'a' || file > 'h' || rank < 1 || rank > 8) {
      return null
    }
    
    return { file, rank }
  }

  public static coordinatesToSquare(file: string, rank: number): string | null {
    if (file < 'a' || file > 'h' || rank < 1 || rank > 8) {
      return null
    }
    
    return `${file}${rank}`
  }

  public static isValidSquare(square: string): boolean {
    return this.squareToCoordinates(square) !== null
  }

  // Get piece symbol for display
  public static getPieceSymbol(piece: string): string {
    const symbols: { [key: string]: string } = {
      'K': '♔', 'Q': '♕', 'R': '♖', 'B': '♗', 'N': '♘', 'P': '♙',
      'k': '♚', 'q': '♛', 'r': '♜', 'b': '♝', 'n': '♞', 'p': '♟'
    }
    return symbols[piece] || piece
  }

  // Get piece name for display
  public static getPieceName(piece: string): string {
    const names: { [key: string]: string } = {
      'K': 'White King', 'Q': 'White Queen', 'R': 'White Rook', 
      'B': 'White Bishop', 'N': 'White Knight', 'P': 'White Pawn',
      'k': 'Black King', 'q': 'Black Queen', 'r': 'Black Rook', 
      'b': 'Black Bishop', 'n': 'Black Knight', 'p': 'Black Pawn'
    }
    return names[piece] || 'Unknown piece'
  }
}
