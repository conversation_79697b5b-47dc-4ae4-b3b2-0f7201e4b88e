{"name": "chessly", "version": "1.0.0", "description": "An undetectable desktop chess assistant that provides the best move suggestions by analyzing chess board screenshots.", "main": "./out/main/index.js", "author": "Chessly Team", "homepage": "https://github.com/chessly/chessly", "scripts": {"format": "prettier --write .", "lint": "eslint --cache .", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "build": "npm run typecheck && electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "electron-vite build && electron-builder --mac", "build:linux": "electron-vite build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.2", "@electron-toolkit/utils": "^4.0.0", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-toast": "^1.2.14", "@tailwindcss/postcss": "^4.1.8", "@tanstack/react-query": "^5.80.6", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.513.0", "postcss": "^8.5.4", "screenshot-desktop": "^1.15.1", "stockfish": "^16.1.0", "chess.js": "^1.0.0-beta.8", "jimp": "^1.6.0", "opencv4nodejs": "^6.0.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "zod": "^3.25.55"}, "devDependencies": {"@electron-toolkit/eslint-config-prettier": "^3.0.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/notarize": "^3.0.1", "@types/node": "^22.15.30", "@types/react": "^19.1.6", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.1", "electron": "36.4.0", "electron-builder": "^26.0.12", "electron-vite": "^3.1.0", "eslint": "^9.28.0", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "prettier": "^3.5.3", "react": "^19.1.0", "react-dom": "^19.1.0", "typescript": "^5.8.3", "vite": "^6.3.5"}, "pnpm": {"onlyBuiltDependencies": ["electron", "esbuild"]}}