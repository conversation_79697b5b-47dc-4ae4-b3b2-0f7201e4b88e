import Jimp from 'jimp'
import { Chess } from 'chess.js'

export interface ChessSquare {
  file: string
  rank: number
  piece: string | null
  x: number
  y: number
  width: number
  height: number
}

export interface ChessBoardDetection {
  squares: ChessSquare[]
  fen: string
  boardBounds: {
    x: number
    y: number
    width: number
    height: number
  }
  playerColor: 'white' | 'black'
  confidence: number
}

export class ChessBoardDetector {
  private readonly PIECE_PATTERNS = {
    // Chess.com piece patterns (simplified color detection)
    white: {
      king: { r: [200, 255], g: [200, 255], b: [200, 255] },
      queen: { r: [200, 255], g: [200, 255], b: [200, 255] },
      rook: { r: [200, 255], g: [200, 255], b: [200, 255] },
      bishop: { r: [200, 255], g: [200, 255], b: [200, 255] },
      knight: { r: [200, 255], g: [200, 255], b: [200, 255] },
      pawn: { r: [200, 255], g: [200, 255], b: [200, 255] }
    },
    black: {
      king: { r: [0, 100], g: [0, 100], b: [0, 100] },
      queen: { r: [0, 100], g: [0, 100], b: [0, 100] },
      rook: { r: [0, 100], g: [0, 100], b: [0, 100] },
      bishop: { r: [0, 100], g: [0, 100], b: [0, 100] },
      knight: { r: [0, 100], g: [0, 100], b: [0, 100] },
      pawn: { r: [0, 100], g: [0, 100], b: [0, 100] }
    }
  }

  public async detectChessBoard(imagePath: string): Promise<ChessBoardDetection | null> {
    try {
      const image = await Jimp.read(imagePath)
      
      // Find the chess board in the image
      const boardBounds = await this.findBoardBounds(image)
      if (!boardBounds) {
        console.log('Could not detect chess board bounds')
        return null
      }

      // Extract squares from the board
      const squares = this.extractSquares(image, boardBounds)
      
      // Detect pieces on each square
      const detectedSquares = await this.detectPieces(image, squares)
      
      // Determine player orientation
      const playerColor = this.determinePlayerColor(detectedSquares)
      
      // Generate FEN string
      const fen = this.generateFEN(detectedSquares, playerColor)
      
      // Calculate confidence based on detected pieces
      const confidence = this.calculateConfidence(detectedSquares)

      return {
        squares: detectedSquares,
        fen,
        boardBounds,
        playerColor,
        confidence
      }
    } catch (error) {
      console.error('Error detecting chess board:', error)
      return null
    }
  }

  private async findBoardBounds(image: Jimp): Promise<{ x: number; y: number; width: number; height: number } | null> {
    const width = image.getWidth()
    const height = image.getHeight()
    
    // Look for chess board patterns - typically square regions with alternating colors
    // This is a simplified approach - in practice, you'd use more sophisticated computer vision
    
    // Common chess board sizes and positions for popular sites
    const commonSizes = [
      { width: 480, height: 480 }, // Chess.com standard
      { width: 512, height: 512 }, // Lichess standard
      { width: 400, height: 400 }, // Smaller boards
      { width: 600, height: 600 }  // Larger boards
    ]

    for (const size of commonSizes) {
      // Try to find a square region that looks like a chess board
      for (let x = 0; x <= width - size.width; x += 20) {
        for (let y = 0; y <= height - size.height; y += 20) {
          if (this.looksLikeChessBoard(image, x, y, size.width, size.height)) {
            return { x, y, width: size.width, height: size.height }
          }
        }
      }
    }

    // Fallback: assume the largest square region in the center
    const minDimension = Math.min(width, height)
    const boardSize = Math.floor(minDimension * 0.8)
    const x = Math.floor((width - boardSize) / 2)
    const y = Math.floor((height - boardSize) / 2)
    
    return { x, y, width: boardSize, height: boardSize }
  }

  private looksLikeChessBoard(image: Jimp, x: number, y: number, width: number, height: number): boolean {
    // Check if the region has alternating light/dark squares
    const squareSize = width / 8
    let lightSquares = 0
    let darkSquares = 0
    
    for (let file = 0; file < 8; file++) {
      for (let rank = 0; rank < 8; rank++) {
        const squareX = x + file * squareSize + squareSize / 2
        const squareY = y + rank * squareSize + squareSize / 2
        
        const color = image.getPixelColor(Math.floor(squareX), Math.floor(squareY))
        const { r, g, b } = Jimp.intToRGBA(color)
        const brightness = (r + g + b) / 3
        
        if ((file + rank) % 2 === 0) {
          // Should be light square
          if (brightness > 150) lightSquares++
        } else {
          // Should be dark square
          if (brightness < 150) darkSquares++
        }
      }
    }
    
    // If most squares match the expected pattern, it's likely a chess board
    return (lightSquares + darkSquares) > 48 // At least 75% match
  }

  private extractSquares(image: Jimp, boardBounds: { x: number; y: number; width: number; height: number }): ChessSquare[] {
    const squares: ChessSquare[] = []
    const squareWidth = boardBounds.width / 8
    const squareHeight = boardBounds.height / 8
    
    for (let file = 0; file < 8; file++) {
      for (let rank = 0; rank < 8; rank++) {
        const x = boardBounds.x + file * squareWidth
        const y = boardBounds.y + rank * squareHeight
        
        squares.push({
          file: String.fromCharCode(97 + file), // 'a' to 'h'
          rank: 8 - rank, // 8 to 1 (from top to bottom)
          piece: null,
          x: Math.floor(x),
          y: Math.floor(y),
          width: Math.floor(squareWidth),
          height: Math.floor(squareHeight)
        })
      }
    }
    
    return squares
  }

  private async detectPieces(image: Jimp, squares: ChessSquare[]): Promise<ChessSquare[]> {
    const detectedSquares = [...squares]
    
    for (const square of detectedSquares) {
      // Sample the center of the square to detect pieces
      const centerX = square.x + square.width / 2
      const centerY = square.y + square.height / 2
      
      // Get average color in a small region around the center
      const sampleSize = Math.min(square.width, square.height) * 0.3
      let totalR = 0, totalG = 0, totalB = 0, sampleCount = 0
      
      for (let dx = -sampleSize/2; dx <= sampleSize/2; dx += 2) {
        for (let dy = -sampleSize/2; dy <= sampleSize/2; dy += 2) {
          const x = Math.floor(centerX + dx)
          const y = Math.floor(centerY + dy)
          
          if (x >= 0 && x < image.getWidth() && y >= 0 && y < image.getHeight()) {
            const color = image.getPixelColor(x, y)
            const { r, g, b } = Jimp.intToRGBA(color)
            totalR += r
            totalG += g
            totalB += b
            sampleCount++
          }
        }
      }
      
      if (sampleCount > 0) {
        const avgR = totalR / sampleCount
        const avgG = totalG / sampleCount
        const avgB = totalB / sampleCount
        
        // Detect piece based on color patterns
        square.piece = this.detectPieceFromColor(avgR, avgG, avgB, square)
      }
    }
    
    return detectedSquares
  }

  private detectPieceFromColor(r: number, g: number, b: number, square: ChessSquare): string | null {
    const brightness = (r + g + b) / 3
    
    // Very basic piece detection - this would need to be much more sophisticated
    // For now, we'll use a simple heuristic based on brightness and position
    
    if (brightness < 80) {
      // Likely a black piece
      if (square.rank === 8) {
        // Back rank pieces
        if (square.file === 'a' || square.file === 'h') return 'r'
        if (square.file === 'b' || square.file === 'g') return 'n'
        if (square.file === 'c' || square.file === 'f') return 'b'
        if (square.file === 'd') return 'q'
        if (square.file === 'e') return 'k'
      } else if (square.rank === 7) {
        return 'p'
      }
    } else if (brightness > 180) {
      // Likely a white piece
      if (square.rank === 1) {
        // Back rank pieces
        if (square.file === 'a' || square.file === 'h') return 'R'
        if (square.file === 'b' || square.file === 'g') return 'N'
        if (square.file === 'c' || square.file === 'f') return 'B'
        if (square.file === 'd') return 'Q'
        if (square.file === 'e') return 'K'
      } else if (square.rank === 2) {
        return 'P'
      }
    }
    
    return null
  }

  private determinePlayerColor(squares: ChessSquare[]): 'white' | 'black' {
    // Look for the king position to determine orientation
    const whiteKing = squares.find(s => s.piece === 'K')
    const blackKing = squares.find(s => s.piece === 'k')
    
    if (whiteKing && whiteKing.rank === 1) {
      return 'white' // White pieces at bottom
    } else if (blackKing && blackKing.rank === 1) {
      return 'black' // Black pieces at bottom (flipped board)
    }
    
    // Default to white
    return 'white'
  }

  private generateFEN(squares: ChessSquare[], playerColor: 'white' | 'black'): string {
    let fen = ''
    
    // Build the board part of FEN
    for (let rank = 8; rank >= 1; rank--) {
      let emptyCount = 0
      
      for (let file = 0; file < 8; file++) {
        const square = squares.find(s => 
          s.file === String.fromCharCode(97 + file) && s.rank === rank
        )
        
        if (square?.piece) {
          if (emptyCount > 0) {
            fen += emptyCount.toString()
            emptyCount = 0
          }
          fen += square.piece
        } else {
          emptyCount++
        }
      }
      
      if (emptyCount > 0) {
        fen += emptyCount.toString()
      }
      
      if (rank > 1) {
        fen += '/'
      }
    }
    
    // Add game state (simplified)
    fen += ` ${playerColor === 'white' ? 'w' : 'b'} KQkq - 0 1`
    
    return fen
  }

  private calculateConfidence(squares: ChessSquare[]): number {
    const totalSquares = 64
    const occupiedSquares = squares.filter(s => s.piece !== null).length
    
    // Basic confidence based on number of detected pieces
    // A typical chess game should have 16-32 pieces
    if (occupiedSquares >= 16 && occupiedSquares <= 32) {
      return Math.min(0.9, occupiedSquares / 32)
    } else if (occupiedSquares > 0) {
      return Math.max(0.3, occupiedSquares / 32)
    }
    
    return 0.1
  }
}
