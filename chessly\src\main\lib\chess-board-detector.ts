import fs from 'fs'
import path from 'path'

export interface ChessSquare {
  file: string
  rank: number
  piece: string | null
  x: number
  y: number
  width: number
  height: number
}

export interface ChessBoardDetection {
  squares: ChessSquare[]
  fen: string
  boardBounds: {
    x: number
    y: number
    width: number
    height: number
  }
  playerColor: 'white' | 'black'
  confidence: number
}

export class ChessBoardDetector {
  private readonly PIECE_PATTERNS = {
    // Chess.com piece patterns (simplified color detection)
    white: {
      king: { r: [200, 255], g: [200, 255], b: [200, 255] },
      queen: { r: [200, 255], g: [200, 255], b: [200, 255] },
      rook: { r: [200, 255], g: [200, 255], b: [200, 255] },
      bishop: { r: [200, 255], g: [200, 255], b: [200, 255] },
      knight: { r: [200, 255], g: [200, 255], b: [200, 255] },
      pawn: { r: [200, 255], g: [200, 255], b: [200, 255] }
    },
    black: {
      king: { r: [0, 100], g: [0, 100], b: [0, 100] },
      queen: { r: [0, 100], g: [0, 100], b: [0, 100] },
      rook: { r: [0, 100], g: [0, 100], b: [0, 100] },
      bishop: { r: [0, 100], g: [0, 100], b: [0, 100] },
      knight: { r: [0, 100], g: [0, 100], b: [0, 100] },
      pawn: { r: [0, 100], g: [0, 100], b: [0, 100] }
    }
  }

  public async detectChessBoard(imagePath: string): Promise<ChessBoardDetection | null> {
    try {
      // For now, return a mock detection result
      console.log('Mock chess board detection for:', imagePath)

      // Check if file exists
      if (!fs.existsSync(imagePath)) {
        console.error('Screenshot file does not exist:', imagePath)
        return null
      }

      // Mock board bounds (assuming a typical chess.com layout)
      const boardBounds = {
        x: 100,
        y: 100,
        width: 480,
        height: 480
      }

      // Generate mock squares
      const squares = this.generateMockSquares(boardBounds)

      // Mock piece detection with starting position
      const detectedSquares = this.generateStartingPosition(squares)

      // Generate FEN string
      const fen = this.generateFEN(detectedSquares, 'white')

      return {
        squares: detectedSquares,
        fen,
        boardBounds,
        playerColor: 'white',
        confidence: 0.85 // Mock confidence
      }
    } catch (error) {
      console.error('Error detecting chess board:', error)
      return null
    }
  }

  private generateMockSquares(boardBounds: { x: number; y: number; width: number; height: number }): ChessSquare[] {
    const squares: ChessSquare[] = []
    const squareWidth = boardBounds.width / 8
    const squareHeight = boardBounds.height / 8

    for (let file = 0; file < 8; file++) {
      for (let rank = 0; rank < 8; rank++) {
        const x = boardBounds.x + file * squareWidth
        const y = boardBounds.y + rank * squareHeight

        squares.push({
          file: String.fromCharCode(97 + file), // 'a' to 'h'
          rank: 8 - rank, // 8 to 1 (from top to bottom)
          piece: null,
          x: Math.floor(x),
          y: Math.floor(y),
          width: Math.floor(squareWidth),
          height: Math.floor(squareHeight)
        })
      }
    }

    return squares
  }

  private generateStartingPosition(squares: ChessSquare[]): ChessSquare[] {
    const detectedSquares = [...squares]

    // Set up starting chess position
    const startingPieces: { [key: string]: string } = {
      'a8': 'r', 'b8': 'n', 'c8': 'b', 'd8': 'q', 'e8': 'k', 'f8': 'b', 'g8': 'n', 'h8': 'r',
      'a7': 'p', 'b7': 'p', 'c7': 'p', 'd7': 'p', 'e7': 'p', 'f7': 'p', 'g7': 'p', 'h7': 'p',
      'a2': 'P', 'b2': 'P', 'c2': 'P', 'd2': 'P', 'e2': 'P', 'f2': 'P', 'g2': 'P', 'h2': 'P',
      'a1': 'R', 'b1': 'N', 'c1': 'B', 'd1': 'Q', 'e1': 'K', 'f1': 'B', 'g1': 'N', 'h1': 'R'
    }

    for (const square of detectedSquares) {
      const squareName = `${square.file}${square.rank}`
      square.piece = startingPieces[squareName] || null
    }

    return detectedSquares
  }



  private determinePlayerColor(squares: ChessSquare[]): 'white' | 'black' {
    // Look for the king position to determine orientation
    const whiteKing = squares.find(s => s.piece === 'K')
    const blackKing = squares.find(s => s.piece === 'k')
    
    if (whiteKing && whiteKing.rank === 1) {
      return 'white' // White pieces at bottom
    } else if (blackKing && blackKing.rank === 1) {
      return 'black' // Black pieces at bottom (flipped board)
    }
    
    // Default to white
    return 'white'
  }

  private generateFEN(squares: ChessSquare[], playerColor: 'white' | 'black'): string {
    let fen = ''
    
    // Build the board part of FEN
    for (let rank = 8; rank >= 1; rank--) {
      let emptyCount = 0
      
      for (let file = 0; file < 8; file++) {
        const square = squares.find(s => 
          s.file === String.fromCharCode(97 + file) && s.rank === rank
        )
        
        if (square?.piece) {
          if (emptyCount > 0) {
            fen += emptyCount.toString()
            emptyCount = 0
          }
          fen += square.piece
        } else {
          emptyCount++
        }
      }
      
      if (emptyCount > 0) {
        fen += emptyCount.toString()
      }
      
      if (rank > 1) {
        fen += '/'
      }
    }
    
    // Add game state (simplified)
    fen += ` ${playerColor === 'white' ? 'w' : 'b'} KQkq - 0 1`
    
    return fen
  }

  private calculateConfidence(squares: ChessSquare[]): number {
    const totalSquares = 64
    const occupiedSquares = squares.filter(s => s.piece !== null).length
    
    // Basic confidence based on number of detected pieces
    // A typical chess game should have 16-32 pieces
    if (occupiedSquares >= 16 && occupiedSquares <= 32) {
      return Math.min(0.9, occupiedSquares / 32)
    } else if (occupiedSquares > 0) {
      return Math.max(0.3, occupiedSquares / 32)
    }
    
    return 0.1
  }
}
