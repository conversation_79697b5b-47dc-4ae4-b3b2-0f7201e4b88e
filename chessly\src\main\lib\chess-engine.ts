import { Chess } from 'chess.js'
import { spawn, ChildProcess } from 'child_process'
import path from 'path'
import { app } from 'electron'

export interface ChessMove {
  from: string
  to: string
  promotion?: string
  san: string
  uci: string
}

export interface ChessAnalysis {
  bestMove: ChessMove | null
  evaluation: number
  depth: number
  pv: string[]
  mate?: number
}

export class ChessEngine {
  private stockfish: ChildProcess | null = null
  private chess: Chess
  private isReady = false
  private analysisCallback: ((analysis: ChessAnalysis) => void) | null = null

  constructor() {
    this.chess = new Chess()
    this.initializeEngine()
  }

  private async initializeEngine(): Promise<void> {
    try {
      // Try to use bundled Stockfish first, fallback to system Stockfish
      const stockfishPath = this.getStockfishPath()
      this.stockfish = spawn(stockfishPath)

      this.stockfish.stdout?.on('data', (data: Buffer) => {
        this.handleEngineOutput(data.toString())
      })

      this.stockfish.stderr?.on('data', (data: Buffer) => {
        console.error('Stockfish error:', data.toString())
      })

      this.stockfish.on('error', (error) => {
        console.error('Failed to start Stockfish:', error)
        this.fallbackToWebStockfish()
      })

      // Initialize engine
      this.sendCommand('uci')
      this.sendCommand('isready')
    } catch (error) {
      console.error('Error initializing chess engine:', error)
      this.fallbackToWebStockfish()
    }
  }

  private getStockfishPath(): string {
    const platform = process.platform
    const arch = process.arch

    // Try bundled Stockfish first
    if (platform === 'win32') {
      return path.join(app.getAppPath(), 'resources', 'stockfish', 'stockfish.exe')
    } else if (platform === 'darwin') {
      return path.join(app.getAppPath(), 'resources', 'stockfish', 'stockfish')
    } else {
      return path.join(app.getAppPath(), 'resources', 'stockfish', 'stockfish')
    }
  }

  private fallbackToWebStockfish(): void {
    // Fallback to web-based Stockfish if native fails
    console.log('Falling back to web-based Stockfish')
    // This would use the stockfish npm package
    try {
      const Stockfish = require('stockfish')
      this.stockfish = Stockfish() as any
      this.stockfish.postMessage = this.stockfish.postMessage || ((msg: string) => {
        if (this.stockfish && typeof this.stockfish.stdin?.write === 'function') {
          this.stockfish.stdin.write(msg + '\n')
        }
      })
    } catch (error) {
      console.error('Failed to initialize web Stockfish:', error)
    }
  }

  private sendCommand(command: string): void {
    if (this.stockfish && this.stockfish.stdin) {
      this.stockfish.stdin.write(command + '\n')
    }
  }

  private handleEngineOutput(output: string): void {
    const lines = output.trim().split('\n')
    
    for (const line of lines) {
      if (line.includes('uciok')) {
        this.sendCommand('setoption name Threads value 1')
        this.sendCommand('setoption name Hash value 128')
      } else if (line.includes('readyok')) {
        this.isReady = true
        console.log('Chess engine ready')
      } else if (line.startsWith('bestmove')) {
        this.handleBestMove(line)
      } else if (line.startsWith('info')) {
        this.handleAnalysisInfo(line)
      }
    }
  }

  private handleBestMove(line: string): void {
    const parts = line.split(' ')
    const bestMoveUci = parts[1]
    
    if (bestMoveUci && bestMoveUci !== '(none)') {
      try {
        const move = this.chess.move(bestMoveUci)
        if (move && this.analysisCallback) {
          const analysis: ChessAnalysis = {
            bestMove: {
              from: move.from,
              to: move.to,
              promotion: move.promotion,
              san: move.san,
              uci: bestMoveUci
            },
            evaluation: 0,
            depth: 0,
            pv: [bestMoveUci]
          }
          this.analysisCallback(analysis)
        }
        // Undo the move to keep the position
        this.chess.undo()
      } catch (error) {
        console.error('Error processing best move:', error)
      }
    }
  }

  private handleAnalysisInfo(line: string): void {
    // Parse engine analysis info
    const parts = line.split(' ')
    let depth = 0
    let evaluation = 0
    let pv: string[] = []
    let mate: number | undefined

    for (let i = 0; i < parts.length; i++) {
      if (parts[i] === 'depth') {
        depth = parseInt(parts[i + 1]) || 0
      } else if (parts[i] === 'cp') {
        evaluation = parseInt(parts[i + 1]) || 0
      } else if (parts[i] === 'mate') {
        mate = parseInt(parts[i + 1]) || 0
      } else if (parts[i] === 'pv') {
        pv = parts.slice(i + 1)
        break
      }
    }

    // Only send significant analysis updates
    if (depth >= 10 && this.analysisCallback) {
      const analysis: ChessAnalysis = {
        bestMove: null,
        evaluation: evaluation / 100, // Convert centipawns to pawns
        depth,
        pv,
        mate
      }
      this.analysisCallback(analysis)
    }
  }

  public setPosition(fen: string): boolean {
    try {
      this.chess.load(fen)
      if (this.isReady) {
        this.sendCommand(`position fen ${fen}`)
      }
      return true
    } catch (error) {
      console.error('Invalid FEN:', error)
      return false
    }
  }

  public analyzePosition(callback: (analysis: ChessAnalysis) => void, depth = 15): void {
    if (!this.isReady) {
      console.error('Chess engine not ready')
      return
    }

    this.analysisCallback = callback
    this.sendCommand(`go depth ${depth}`)
  }

  public stopAnalysis(): void {
    if (this.isReady) {
      this.sendCommand('stop')
    }
    this.analysisCallback = null
  }

  public getCurrentPosition(): string {
    return this.chess.fen()
  }

  public isValidMove(from: string, to: string, promotion?: string): boolean {
    try {
      const move = this.chess.move({ from, to, promotion })
      if (move) {
        this.chess.undo()
        return true
      }
      return false
    } catch {
      return false
    }
  }

  public destroy(): void {
    this.stopAnalysis()
    if (this.stockfish) {
      this.stockfish.kill()
      this.stockfish = null
    }
  }
}
