import { useState } from 'react'
import { X, ExternalLink, Play, Target } from 'lucide-react'
import { cn } from '../lib/utils'

interface InstructionsDialogProps {
  isOpen: boolean
  onClose: () => void
}

export function InstructionsDialog({ isOpen, onClose }: InstructionsDialogProps) {
  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-gray-900 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <div className="flex items-center gap-2">
            <Target className="w-6 h-6 text-blue-400" />
            <h2 className="text-xl font-bold text-white">How to Use Chessly</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg hover:bg-gray-700 transition-colors"
          >
            <X className="w-5 h-5 text-gray-400" />
          </button>
        </div>

        <div className="p-6 space-y-6 text-gray-300">
          <div className="bg-blue-900/30 border border-blue-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-blue-400 mb-2">🚀 Real-time Mode (Recommended)</h3>
            <p className="text-sm mb-3">
              Get instant move suggestions as you play - just like the browser extension!
            </p>
            <ol className="space-y-2 text-sm">
              <li className="flex items-start gap-2">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">1</span>
                <div>
                  <strong>Open a chess website</strong> in your browser:
                  <ul className="ml-4 mt-1 space-y-1">
                    <li className="flex items-center gap-2">
                      <ExternalLink className="w-3 h-3" />
                      <a href="#" className="text-blue-400 hover:underline">chess.com</a>
                    </li>
                    <li className="flex items-center gap-2">
                      <ExternalLink className="w-3 h-3" />
                      <a href="#" className="text-blue-400 hover:underline">lichess.org</a>
                    </li>
                  </ul>
                </div>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">2</span>
                <span><strong>Start a chess game</strong> (any time control)</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">3</span>
                <span>In Chessly, click <strong>"Play White"</strong> or <strong>"Play Black"</strong> based on your color</span>
              </li>
              <li className="flex items-start gap-2">
                <span className="bg-blue-600 text-white rounded-full w-5 h-5 flex items-center justify-center text-xs font-bold mt-0.5">4</span>
                <span><strong>That's it!</strong> Chessly will automatically detect moves and show the best response</span>
              </li>
            </ol>
          </div>

          <div className="bg-green-900/30 border border-green-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-green-400 mb-2">✨ What Happens Next</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Chessly monitors the chess board in real-time</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>When your opponent moves, it instantly analyzes the position</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Best moves are highlighted directly on the chess board</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Move suggestions appear in the Chessly window</span>
              </li>
            </ul>
          </div>

          <div className="bg-yellow-900/30 border border-yellow-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-yellow-400 mb-2">📷 Manual Mode (Backup)</h3>
            <p className="text-sm mb-2">
              If real-time mode doesn't work, you can use manual screenshot analysis:
            </p>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span>Click "Capture" to take a screenshot</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <span>Chessly will analyze the chess board and suggest moves</span>
              </li>
            </ul>
          </div>

          <div className="bg-red-900/30 border border-red-700 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-red-400 mb-2">⚠️ Important Notes</h3>
            <ul className="space-y-1 text-sm">
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <span>Use responsibly - this is for learning and analysis purposes</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <span>Make sure your browser is visible and the chess board is not covered</span>
              </li>
              <li className="flex items-center gap-2">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <span>If monitoring fails, try refreshing the chess website</span>
              </li>
            </ul>
          </div>

          <div className="bg-gray-800 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-300 mb-2">🎮 Keyboard Shortcuts</h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <div className="flex justify-between">
                  <span>Take Screenshot:</span>
                  <code className="bg-gray-700 px-2 py-1 rounded">Ctrl+H</code>
                </div>
              </div>
              <div>
                <div className="flex justify-between">
                  <span>Toggle Visibility:</span>
                  <code className="bg-gray-700 px-2 py-1 rounded">Ctrl+B</code>
                </div>
              </div>
              <div>
                <div className="flex justify-between">
                  <span>Reset Analysis:</span>
                  <code className="bg-gray-700 px-2 py-1 rounded">Ctrl+R</code>
                </div>
              </div>
              <div>
                <div className="flex justify-between">
                  <span>Quit App:</span>
                  <code className="bg-gray-700 px-2 py-1 rounded">Ctrl+Q</code>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-4 rounded-lg transition-colors flex items-center justify-center gap-2"
          >
            <Play className="w-4 h-4" />
            Got it! Let's start playing
          </button>
        </div>
      </div>
    </div>
  )
}
