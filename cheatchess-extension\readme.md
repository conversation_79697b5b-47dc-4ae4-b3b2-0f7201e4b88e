<h1>Chess Cheat</h1>
<p>Chess cheat utilizes the stockfish engine and the power of your computer to show you the best move to make in any chess.com game. It guarantees you wins in atleast 90% of games you play and can greatly help you improve your chess skills.

<i>Note: this project was developed for learning purposes, I do not condone or encourage cheating in games and this project should help you get better at chess. Non fair play might result in your chess.com account being suspended if you do not use wisely. </i>
<br>


https://user-images.githubusercontent.com/********/*********-244544b0-8070-4ee3-973f-c4c9bfa15067.mp4


<h1>How to install:</h1>
First clone this repo if you have git installed on your machine:
<code>
git clone https://github.com/Eugenenoble2005/chesscheat.git
</code>

If you do not have git installed, you may download this repo as a zip by following this link:
<a href = "https://github.com/Eugenenoble2005/chesscheat/archive/refs/heads/master.zip">Zip Download</a>


Open google chrome and visit 'chrome://extensions'.
Enable developer mode by clicking the toggle at the top right. Click 'Load Unpacked' and finally select the folder where you have cloned or extracted the repo. Activate the extension and done.