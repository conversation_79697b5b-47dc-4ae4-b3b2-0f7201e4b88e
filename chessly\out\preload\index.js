"use strict";
Object.defineProperty(exports, Symbol.toStringTag, { value: "Module" });
const electron = require("electron");
const CHESS_EVENTS = {
  NO_SCREENSHOTS: "chess-no-screenshots",
  BOARD_DETECTED: "chess-board-detected",
  ANALYSIS_START: "chess-analysis-start",
  ANALYSIS_UPDATE: "chess-analysis-update",
  BEST_MOVE_FOUND: "chess-best-move-found",
  ANALYSIS_ERROR: "chess-analysis-error",
  MOVE_HIGHLIGHT: "chess-move-highlight",
  RESET: "chess-reset-view",
  FORCE_ANALYSIS: "chess-force-analysis"
};
const electronAPI = {
  getConfig: () => electron.ipcRenderer.invoke("get-config"),
  updateConfig: (config) => electron.ipcRenderer.invoke("update-config", config),
  getScreenshots: () => electron.ipcRenderer.invoke("get-screenshots"),
  deleteScreenshot: (path) => electron.ipcRenderer.invoke("delete-screenshot", path),
  toggleMainWindow: async () => {
    console.log("toggleMainWindow called from preload");
    try {
      const result = await electron.ipcRenderer.invoke("toggle-main-window");
      return result;
    } catch (error) {
      console.error("Error toggling main window:", error);
      throw error;
    }
  },
  // Chess-specific API methods
  getCurrentPosition: () => electron.ipcRenderer.invoke("get-current-position"),
  getIsAnalyzing: () => electron.ipcRenderer.invoke("get-is-analyzing"),
  stopChessAnalysis: () => electron.ipcRenderer.invoke("stop-chess-analysis"),
  getBestMove: () => electron.ipcRenderer.invoke("get-best-move"),
  getEvaluation: () => electron.ipcRenderer.invoke("get-evaluation"),
  getSquareInfo: (file, rank) => electron.ipcRenderer.invoke("get-square-info", file, rank),
  getAllSquares: () => electron.ipcRenderer.invoke("get-all-squares"),
  triggerChessAnalysis: () => electron.ipcRenderer.invoke("trigger-chess-analysis"),
  // Real-time monitoring API methods
  startRealtimeMonitoring: () => electron.ipcRenderer.invoke("start-realtime-monitoring"),
  stopRealtimeMonitoring: () => electron.ipcRenderer.invoke("stop-realtime-monitoring"),
  getMonitoringState: () => electron.ipcRenderer.invoke("get-monitoring-state"),
  startQuickMode: (playerColor) => electron.ipcRenderer.invoke("start-quick-mode", playerColor),
  simulateGame: () => electron.ipcRenderer.invoke("simulate-game"),
  analyzeManualPosition: (fen) => electron.ipcRenderer.invoke("analyze-manual-position", fen),
  onScreenshotTaken: (callback) => {
    const subscription = (_, data) => callback(data);
    electron.ipcRenderer.on("screenshot-taken", subscription);
    return () => electron.ipcRenderer.removeListener("screenshot-taken", subscription);
  },
  getPlatform: () => process.platform,
  triggerScreenshot: () => electron.ipcRenderer.invoke("trigger-screenshot"),
  deleteLastScreenshot: () => electron.ipcRenderer.invoke("delete-last-screenshot"),
  // Chess event handlers
  onChessBoardDetected: (callback) => {
    const subscription = (_, position) => callback(position);
    electron.ipcRenderer.on(CHESS_EVENTS.BOARD_DETECTED, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.BOARD_DETECTED, subscription);
  },
  onChessAnalysisStart: (callback) => {
    const subscription = (_, position) => callback(position);
    electron.ipcRenderer.on(CHESS_EVENTS.ANALYSIS_START, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_START, subscription);
  },
  onChessAnalysisUpdate: (callback) => {
    const subscription = (_, data) => callback(data);
    electron.ipcRenderer.on(CHESS_EVENTS.ANALYSIS_UPDATE, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_UPDATE, subscription);
  },
  onChessMoveHighlight: (callback) => {
    const subscription = (_, data) => callback(data);
    electron.ipcRenderer.on(CHESS_EVENTS.MOVE_HIGHLIGHT, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.MOVE_HIGHLIGHT, subscription);
  },
  onChessAnalysisError: (callback) => {
    const subscription = (_, error) => callback(error);
    electron.ipcRenderer.on(CHESS_EVENTS.ANALYSIS_ERROR, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.ANALYSIS_ERROR, subscription);
  },
  onChessNoScreenshots: (callback) => {
    const subscription = () => callback();
    electron.ipcRenderer.on(CHESS_EVENTS.NO_SCREENSHOTS, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.NO_SCREENSHOTS, subscription);
  },
  onChessReset: (callback) => {
    const subscription = () => callback();
    electron.ipcRenderer.on(CHESS_EVENTS.RESET, subscription);
    return () => electron.ipcRenderer.removeListener(CHESS_EVENTS.RESET, subscription);
  },
  // Real-time monitoring event handlers
  onRealtimeMonitoringStarted: (callback) => {
    const subscription = (_, data) => callback(data);
    electron.ipcRenderer.on("realtime-monitoring-started", subscription);
    return () => electron.ipcRenderer.removeListener("realtime-monitoring-started", subscription);
  },
  onRealtimeMonitoringStopped: (callback) => {
    const subscription = () => callback();
    electron.ipcRenderer.on("realtime-monitoring-stopped", subscription);
    return () => electron.ipcRenderer.removeListener("realtime-monitoring-stopped", subscription);
  },
  onRealtimeMoveSuggestion: (callback) => {
    const subscription = (_, data) => callback(data);
    electron.ipcRenderer.on("realtime-move-suggestion", subscription);
    return () => electron.ipcRenderer.removeListener("realtime-move-suggestion", subscription);
  },
  triggerReset: () => electron.ipcRenderer.invoke("trigger-reset"),
  updateContentDimensions: (dimensions) => electron.ipcRenderer.invoke("update-content-dimensions", dimensions),
  openLink: (url) => electron.ipcRenderer.invoke("openLink", url),
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  removeListener: (eventName, callback) => {
    electron.ipcRenderer.removeListener(eventName, callback);
  },
  removeAllListeners: () => {
    Object.values(CHESS_EVENTS).forEach((event) => {
      electron.ipcRenderer.removeAllListeners(event);
    });
    electron.ipcRenderer.removeAllListeners("screenshot-taken");
    electron.ipcRenderer.removeAllListeners("screenshot-deleted");
  }
};
electron.contextBridge.exposeInMainWorld("electronAPI", electronAPI);
exports.CHESS_EVENTS = CHESS_EVENTS;
